/**
 * 华为云 OBS 浏览器端 SDK 类型声明
 * 基于 esdk-obs-browserjs
 */
declare module 'esdk-obs-browserjs' {
  /**
   * OBS 客户端配置
   */
  interface ObsClientConfig {
    /** 访问密钥 ID */
    access_key_id: string
    /** 访问密钥 Secret */
    secret_access_key: string
    /** 安全令牌（临时凭证时需要） */
    security_token?: string
    /** 服务端点 */
    server: string
    /** 是否使用 HTTPS */
    is_secure?: boolean
    /** 签名版本 */
    signature?: 'v2' | 'v4'
    /** 路径样式 */
    path_style?: boolean
    /** 最大重试次数 */
    max_retry_count?: number
    /** 超时时间（毫秒） */
    timeout?: number
  }

  /**
   * 通用回调函数类型
   */
  type ObsCallback<T = any> = (err: ObsError | null, result?: T) => void

  /**
   * OBS 错误类型
   */
  interface ObsError {
    /** 错误代码 */
    code: string
    /** 错误消息 */
    message: string
    /** HTTP 状态码 */
    status?: number
    /** 请求 ID */
    request_id?: string
    /** 主机 ID */
    host_id?: string
    /** 资源 */
    resource?: string
  }

  /**
   * 通用响应头
   */
  interface CommonHeaders {
    /** 请求 ID */
    RequestId?: string
    /** 响应时间 */
    ResponseTime?: string
    /** 内容长度 */
    ContentLength?: number
    /** 内容类型 */
    ContentType?: string
    /** ETag */
    ETag?: string
    /** 最后修改时间 */
    LastModified?: string
    /** 版本 ID */
    VersionId?: string
  }

  /**
   * 上传对象参数
   */
  interface PutObjectRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 对象内容 */
    Body?: File | Blob | ArrayBuffer | string
    /** 源文件（兼容旧版本） */
    SourceFile?: File
    /** 内容类型 */
    ContentType?: string
    /** 内容长度 */
    ContentLength?: number
    /** 内容编码 */
    ContentEncoding?: string
    /** 内容语言 */
    ContentLanguage?: string
    /** 内容处置 */
    ContentDisposition?: string
    /** 缓存控制 */
    CacheControl?: string
    /** 过期时间 */
    Expires?: Date | string
    /** ACL 权限 */
    ACL?: 'private' | 'public-read' | 'public-read-write' | 'authenticated-read'
    /** 存储类别 */
    StorageClass?: 'STANDARD' | 'WARM' | 'COLD'
    /** 网站重定向位置 */
    WebsiteRedirectLocation?: string
    /** 服务端加密 */
    ServerSideEncryption?: 'AES256' | 'kms'
    /** KMS 密钥 ID */
    SSEKMSKeyId?: string
    /** 自定义元数据 */
    Metadata?: Record<string, string>
    /** 进度回调 */
    ProgressCallback?: (transferredAmount: number, totalAmount: number, totalSeconds?: number) => void
  }

  /**
   * 上传对象响应
   */
  interface PutObjectResponse extends CommonHeaders {
    /** ETag */
    ETag: string
    /** 版本 ID */
    VersionId?: string
    /** 服务端加密 */
    ServerSideEncryption?: string
    /** KMS 密钥 ID */
    SSEKMSKeyId?: string
  }

  /**
   * 获取对象参数
   */
  interface GetObjectRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 版本 ID */
    VersionId?: string
    /** 响应内容类型 */
    ResponseContentType?: string
    /** 响应内容语言 */
    ResponseContentLanguage?: string
    /** 响应过期时间 */
    ResponseExpires?: string
    /** 响应缓存控制 */
    ResponseCacheControl?: string
    /** 响应内容处置 */
    ResponseContentDisposition?: string
    /** 响应内容编码 */
    ResponseContentEncoding?: string
  }

  /**
   * 获取对象元数据参数
   */
  interface GetObjectMetadataRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 版本 ID */
    VersionId?: string
  }

  /**
   * 设置对象元数据参数
   */
  interface SetObjectMetadataRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** ContentDisposition */
    ContentDisposition?: string
    /** 元数据指令 */
    MetadataDirective: string
  }

  /**
   * 获取对象元数据响应
   */
  interface GetObjectMetadataResponse extends CommonHeaders {
    /** 内容长度 */
    ContentLength: number
    /** 内容类型 */
    ContentType: string
    /** 最后修改时间 */
    LastModified: string
    /** ETag */
    ETag: string
    /** 存储类别 */
    StorageClass?: string
    /** 自定义元数据 */
    Metadata?: Record<string, string>
  }

  /**
   * 设置对象元数据响应
   */
  interface SetObjectMetadataResponse extends CommonHeaders {
    /** 内容长度 */
    ContentLength: number
    /** 内容类型 */
    ContentType: string
    /** 最后修改时间 */
    LastModified: string
    /** ETag */
    ETag: string
    /** 存储类别 */
    StorageClass?: string
    /** 自定义元数据 */
    Metadata?: Record<string, string>
  }

  /**
   * 删除对象参数
   */
  interface DeleteObjectRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 版本 ID */
    VersionId?: string
  }

  /**
   * 删除对象响应
   */
  interface DeleteObjectResponse extends CommonHeaders {
    /** 删除标记 */
    DeleteMarker?: boolean
    /** 版本 ID */
    VersionId?: string
  }

  /**
   * 初始化分片上传参数
   */
  interface InitiateMultipartUploadRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 内容类型 */
    ContentType?: string
    /** ACL 权限 */
    ACL?: 'private' | 'public-read' | 'public-read-write' | 'authenticated-read'
    /** 存储类别 */
    StorageClass?: 'STANDARD' | 'WARM' | 'COLD'
    /** 自定义元数据 */
    Metadata?: Record<string, string>
    /** 内容处置 */
    ContentDisposition?: string
  }

  /**
   * 初始化分片上传响应
   */
  interface InitiateMultipartUploadResponse extends CommonHeaders {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 上传 ID */
    UploadId: string
  }

  /**
   * 上传分片参数
   */
  interface UploadPartRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 分片号 */
    PartNumber: number
    /** 上传 ID */
    UploadId: string
    /** 分片内容 */
    Body: File | Blob | ArrayBuffer
    /** 内容长度 */
    ContentLength?: number
  }

  /**
   * 上传分片响应
   */
  interface UploadPartResponse extends CommonHeaders {
    /** ETag */
    ETag: string
  }

  /**
   * 分片信息
   */
  interface Part {
    /** 分片号 */
    PartNumber: number
    /** ETag */
    ETag: string
  }

  /**
   * 完成分片上传参数
   */
  interface CompleteMultipartUploadRequest {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 上传 ID */
    UploadId: string
    /** 分片列表 */
    Parts: Part[]
  }

  /**
   * 完成分片上传响应
   */
  interface CompleteMultipartUploadResponse extends CommonHeaders {
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** ETag */
    ETag: string
    /** 位置 */
    Location: string
    /** 版本 ID */
    VersionId?: string
  }

  /**
   * 创建预签名 URL 参数
   */
  interface CreateSignedUrlRequest {
    /** HTTP 方法 */
    Method: 'GET' | 'PUT' | 'POST' | 'DELETE' | 'HEAD'
    /** 存储桶名称 */
    Bucket: string
    /** 对象键 */
    Key: string
    /** 过期时间（秒） */
    Expires: number
    /** 查询参数 */
    QueryParams?: Record<string, string>
    /** 请求头 */
    Headers?: Record<string, string>
  }

  /**
   * 创建预签名 URL 响应
   */
  interface CreateSignedUrlResponse {
    /** 签名 URL */
    SignedUrl: string
    /** 实际请求头 */
    ActualSignedRequestHeaders?: Record<string, string>
  }

  /**
   * OBS 客户端类
   */
  export default class ObsClient {
    constructor(config: ObsClientConfig)

    /**
     * 上传对象
     */
    putObject(params: PutObjectRequest, callback: ObsCallback<PutObjectResponse>): void

    /**
     * 获取对象
     */
    getObject(params: GetObjectRequest, callback: ObsCallback<any>): void

    /**
     * 设置对象元数据
     */
    setObjectMetadata(params: SetObjectMetadataRequest, callback: ObsCallback<SetObjectMetadataResponse>): void

    /**
     * 获取对象元数据
     */
    getObjectMetadata(params: GetObjectMetadataRequest, callback: ObsCallback<GetObjectMetadataResponse>): void

    /**
     * 删除对象
     */
    deleteObject(params: DeleteObjectRequest, callback: ObsCallback<DeleteObjectResponse>): void

    /**
     * 初始化分片上传
     */
    initiateMultipartUpload(params: InitiateMultipartUploadRequest, callback: ObsCallback<InitiateMultipartUploadResponse>): void

    /**
     * 上传分片
     */
    uploadPart(params: UploadPartRequest, callback: ObsCallback<UploadPartResponse>): void

    /**
     * 完成分片上传
     */
    completeMultipartUpload(params: CompleteMultipartUploadRequest, callback: ObsCallback<CompleteMultipartUploadResponse>): void

    /**
     * 创建预签名 URL（同步方法）
     */
    createSignedUrlSync(params: CreateSignedUrlRequest, callback: ObsCallback<CreateSignedUrlResponse>): void

    /**
     * 关闭客户端
     */
    close(): void
  }
}

/**
 * 扩展全局类型
 */
declare global {
  namespace OBS {
    /**
     * 文件项接口
     */
    interface FileItem {
      /** 文件 ID */
      id: string
      /** 文件名 */
      name: string
      /** 访问 URL */
      url: string
      /** 文件大小 */
      size?: number
      /** 文件类型 */
      type?: string
      /** 上传状态 */
      status?: 'uploading' | 'finished' | 'error'
      /** 上传进度 */
      percentage?: number
      /** ETag */
      etag?: string
      /** 版本 ID */
      versionId?: string
    }

    /**
     * OBS 配置接口
     */
    interface Config {
      /** 访问密钥 ID */
      accessKeyId: string
      /** 访问密钥 Secret */
      accessKeySecret: string
      /** 安全令牌 */
      securityToken?: string
      /** 区域 */
      region: string
      /** 存储桶名称 */
      bucket: string
      /** 服务端点 */
      endpoint?: string
      /** 自定义域名 */
      customDomain?: string
    }

    /**
     * 上传选项接口
     */
    interface UploadOptions {
      /** 文件对象 */
      file: File
      /** 文件路径 */
      key?: string
      /** 进度回调 */
      onProgress?: (progress: number) => void
      /** 内容类型 */
      contentType?: string
      /** 自定义元数据 */
      metadata?: Record<string, string>
      /** ACL 权限 */
      acl?: 'private' | 'public-read' | 'public-read-write'
      /** 存储类别 */
      storageClass?: 'STANDARD' | 'WARM' | 'COLD'
    }

    /**
     * 上传结果接口
     */
    interface UploadResult {
      /** 访问 URL */
      url: string
      /** 文件路径 */
      key: string
      /** 文件大小 */
      size: number
      /** 内容类型 */
      contentType: string
      /** 上传时间 */
      uploadTime: Date
      /** ETag */
      etag?: string
      /** 版本 ID */
      versionId?: string
    }

    /**
     * 常用文件类型
     */
    type FileType = 'IMAGE' | 'AVATAR' | 'DOCUMENT' | 'VIDEO' | 'AUDIO'

    /**
     * ACL 权限类型
     */
    type ACL = 'private' | 'public-read' | 'public-read-write'

    /**
     * 存储类别类型
     */
    type StorageClass = 'STANDARD' | 'WARM' | 'COLD'

    /**
     * 上传状态类型
     */
    type UploadStatus = 'uploading' | 'finished' | 'error'
  }
}
