declare namespace TeachingPlanApi {
  interface GetTeachingPlanContentDemandOutput {
    /** 主键ID */
    Id: string
    /** 标题 */
    Title: string
    /** 描述 */
    Describe: string
    /** 类型（1：系统，2：自定义） */
    Type: number
  }

  interface SaveTeachingPlanContentDemandInput {
    /** 标题 */
    Title?: string
    /** 描述 */
    Describe?: string
    /** 教师Id */
    TeacherId?: string
  }

  interface DelTeachingPlanContentDemandInput {
    /** 内容要求Id */
    Id?: string
    /** 教师Id */
    TeacherId?: string
  }

  interface CreateTeachingPlanInput {
    /** 创建类型（1：标题创建、2：文本创建、3：章节创建、4：文档创建） */
    Type?: number
    /** 年级 */
    Grade?: number
    /** 学科ID */
    SubjectId?: string
    /** 模型ID */
    ModelId?: string
    /** 内容要求Id */
    ContentDemandId?: string[]
    /** 教案标题 */
    Title?: string
    /** 其他要求 */
    Demand?: string
    /** 教案内容 */
    Content?: string
    /** 章节ID */
    ChapterId?: string
    /** 文件地址 */
    FileUrl?: string
    /** 文件名称 */
    FileName?: string
    /** 学校Id */
    SchoolId?: string
    /** 教师Id */
    TeacherId?: string
  }

  interface GetAgentModelInfoOutput {
    /** Id */
    Id: string
    /** 模型名称 */
    ModelName: string
    /** 是否支持上下文 */
    IsContext: boolean
    /** 模型描述 */
    ModelDescribe: string
  }

  interface SaveTeachingPlanInput {
    /** 教案类型（1文本、2word） */
    Type: number
    /** 教案名称（文本类型教案：自定义\教案名称。word类型教案:word文件名称） */
    Name: string
    /** 教案文本（文本类型教案必填） */
    Text?: string
    /** 文件地址（word类型教案必填） */
    FileUrl?: string
    /** 教师Id */
    TeacherId?: string
    /** 学校Id */
    SchoolId?: string
    /** 年级Id */
    GradeId?: string
    /** 学科ID */
    SubjectId?: string
  }

  interface TeachingPlanCreateRecordTextToWordInput {
    /** 教案创建记录Id */
    Id: string
    /** 教师Id */
    TeacherId?: string
  }

  interface UpdateTeachingPlanCreateRecordInput {
    /** Id */
    Id: string
    /** 教师Id */
    TeacherId?: string
    /** 教案文本 */
    TeachingPlanText: string
  }

  interface TeachingPlanCreateRecordFileOutput {
    /** 文件Id */
    Id: string
    /** 教案生成记录Id */
    TeachingPlanCreateRecordId: string
    /** 文件地址 */
    FileUrl: string
    /** 文件名称 */
    FileName: string
    /** 类型（1:word） */
    FileType: number
  }

  interface TeachingPlanCreateRecordFileInput {
    /** 教案生成记录Id */
    Id: string
    /** 教师Id */
    TeacherId?: string
  }

  interface TeachingPlanCreateRecordFileUpdateNameInput {
    /** 文件Id */
    Id?: string
    /** 文件名称 */
    FileName?: string
    /** 教师Id */
    TeacherId?: string
  }

  interface GetTeachingPlanCreateRecordInput {
    /** 页码（默认1） */
    PageIndex?: number
    /** 每页数量（默认10） */
    PageSize?: number
    /** 教师Id */
    TeacherId?: string
    /** 学校Id */
    SchoolId?: string
    /** 学科Id */
    SubjectId?: string
    /** 年级 */
    Grade?: string
  }

  interface GetTeachingPlanCreateRecordOutput {
    /** 教案生成记录Id */
    Id?: string
    /** 教案名称 */
    Name?: string
    /** 教案文本 */
    TeachingPlanText?: string
    /** 创建时间 */
    CreateTime?: string
  }

  interface PageReturn1GetTeachingPlanCreateRecordOutput {
    /** 数据总量 */
    TotalCount?: number
    /** 数据集 */
    Datas?: GetTeachingPlanCreateRecordOutput[]
  }

  interface TeachingPlanCreateRecordOptimizeInput {
    /** 教案创建记录Id */
    Id?: string
    /** 教师Id */
    TeacherId?: string
    /** 优化方案（优化类型） */
    Scheme?: string
    /** 将要优化的原文 */
    OptimizeText?: string
    /** 教师指令 */
    Prompt?: string
  }

  interface TeachingPlanCreateRecordDetailsInput {
    /** 教案创建记录Id */
    Id?: string
  }

  interface TeachingPlanCreateRecordDetailsOutput {
    /** 教案生成记录Id */
    Id?: string
    /** 教案名称 */
    Name?: string
    /** 创建类型（1：标题创建、2：文本创建、3：章节创建、4：文档创建） */
    Type?: number
    /** 年级 */
    Grade?: number
    /** 教案标题（用于标题创建逻辑处理AI指令） */
    Title?: string
    /** 其他要求 */
    Demand?: string
    /** 教案内容 */
    Content?: string
    /** 章节ID */
    ChapterId?: string
    /** 文件地址 */
    FileUrl?: string
    /** 文件名称 */
    FileName?: string
    /** 模型ID */
    ModelId?: string
    /** 学科ID */
    SubjectId?: string
    /** 教案文本 */
    TeachingPlanText?: string
    /** 内容要求Id */
    ContentDemandId?: string[]
  }

  interface TeachingPlanUpdateNameInput {
    /** 教案Id */
    Id?: string
    /** 教案名称 */
    Name?: string
    /** 教师Id */
    TeacherId?: string
  }

  interface GetTeachingPlanInput extends CommonType.RecordNullable<Api.Common.CommonSearchParams> {
    /** 教师Id */
    TeacherId?: string
    /** 学校Id */
    SchoolId?: string
    /** 年级Id */
    GradeId?: string
    /** 学科ID */
    SubjectId?: string
  }

  interface PageReturn1GetTeachingPlanOutput {
    /** 数据总量 */
    TotalCount?: number
    /** 数据集 */
    Datas?: GetTeachingPlanOutput[]
  }

  interface GetTeachingPlanOutput {
    /** 教案Id */
    Id?: string
    /** 教案名称 */
    Name?: string
    /** 创建时间 */
    Createtime?: string
    /** 修改时间 */
    ModifyTime?: string
    /** 教案类型（1文本、2word） */
    Type?: number
    /** 文件Id（用于Office在线编辑） */
    FileId?: string
  }

  interface GetTeachingPlanDetailsOutput {
    /** 教案Id */
    Id: string
    /** 教案名称 */
    Name?: string
    /** 类型（1:文本、2:Word） */
    Type: number
    /** 教案文本内容 */
    Text: string
  }

  interface TeachingPlanTextUpdateInput {
    /** 教案Id */
    Id?: string
    /** 教案名称 */
    Name?: string
    /** 教案文本 */
    Text?: string
    /** 教师Id */
    TeacherId?: string
  }
}
