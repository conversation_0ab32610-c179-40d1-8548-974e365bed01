import { request } from '@/service/request'

/**
 * 获取教案内容要求
 * @param data
 */
export function fetchGetTeachingPlanContentDemand(data = {}) {
  return request<TeachingPlanApi.GetTeachingPlanContentDemandOutput[]>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/GetTeachingPlanContentDemand',
    method: 'POST',
    data,
  })
}

/**
 * 保存教案内容要求
 * @param data
 */
export function fetchSaveTeachingPlanContentDemand(data: TeachingPlanApi.SaveTeachingPlanContentDemandInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/SaveTeachingPlanContentDemand',
    method: 'POST',
    data,
  })
}

/**
 * 删除教案内容要求
 * @param data
 */
export function fetchDelTeachingPlanContentDemand(data: TeachingPlanApi.DelTeachingPlanContentDemandInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/DelTeachingPlanContentDemand',
    method: 'POST',
    data,
  })
}

/**
 * 保存教案
 * @param data
 */
export function fetchSaveTeachingPlan(data: TeachingPlanApi.SaveTeachingPlanInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/SaveTeachingPlan',
    method: 'POST',
    data,
  })
}

/**
 * 生成word教案文档
 * @param data
 */
export function fetchGenerateWordTeachingPlan(data: TeachingPlanApi.TeachingPlanCreateRecordTextToWordInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanCreateRecordTextToWord',
    method: 'POST',
    data,
  })
}

/**
 * 修改教案创建记录中的教案文本
 * @param data
 */
export function fetchUpdateTeachingPlanCreateRecordText(data: TeachingPlanApi.UpdateTeachingPlanCreateRecordInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/UpdateTeachingPlanCreateRecord',
    method: 'POST',
    data,
  })
}

/**
 * 获取教案生成记录文件
 * @param data
 */
export function fetchUpdateTeachingPlanCreateRecordFile(data: TeachingPlanApi.TeachingPlanCreateRecordFileInput) {
  return request<TeachingPlanApi.TeachingPlanCreateRecordFileOutput[]>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanCreateRecordFile',
    method: 'POST',
    data,
  })
}

/**
 * 教案生成记录修改文件名称
 * @param data
 */
export function fetchTeachingPlanCreateRecordFileUpdateName(data: TeachingPlanApi.TeachingPlanCreateRecordFileUpdateNameInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanCreateRecordFileUpdateName',
    method: 'POST',
    data,
  })
}

/**
 * 获取教案创建记录列表
 * @param data
 */
export function fetchTeachingPlanCreateRecord(data: TeachingPlanApi.GetTeachingPlanCreateRecordInput) {
  return request<TeachingPlanApi.PageReturn1GetTeachingPlanCreateRecordOutput>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/GetTeachingPlanCreateRecord',
    method: 'POST',
    data,
  })
}

/**
 * 教案创建记录详情
 * @param data
 */
export function fetchTeachingPlanCreateRecordDetails(data: TeachingPlanApi.TeachingPlanCreateRecordDetailsInput) {
  return request<TeachingPlanApi.TeachingPlanCreateRecordDetailsOutput>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanCreateRecordDetails',
    method: 'POST',
    data,
  })
}

/**
 * 教案修改名称
 * @param data
 */
export function fetchTeachingPlanCreateRecordUpdateName(data: TeachingPlanApi.TeachingPlanUpdateNameInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanUpdateName',
    method: 'POST',
    data,
  })
}

/**
 * 教案列表
 * @param params
 */
export async function fetchGetTeachingPlan(params: TeachingPlanApi.GetTeachingPlanInput) {
  const { current, size, ...extra } = params
  return request<Api.Common.PaginatingQueryRecord<TeachingPlanApi.GetTeachingPlanOutput>>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/GetTeachingPlan',
    method: 'POST',
    data: {
      ...extra,
    },
  }).then((res) => {
    const resp = res as unknown as any

    if (resp?.error) {
      return resp
    }

    return {
      data: {
        records: resp.data.Datas ?? [],
        current: params.current,
        size: params.size,
        total: resp.data.TotalCount ?? 0,
      },
      error: null,
      response: resp.response,
    } as any
  })
}

/**
 * 删除教案
 * @param data
 */
export function fetchDelTeachingPlan(data: {
  Id: string
}) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/DelTeachingPlan',
    method: 'POST',
    data,
  })
}

/**
 * 获取教案详情
 * @param data
 */
export function fetchGetTeachingPlanDetails(data: {
  Id: string
}) {
  return request<TeachingPlanApi.GetTeachingPlanDetailsOutput>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/GetTeachingPlanDetails',
    method: 'POST',
    data,
  })
}

/**
 * 教案文本类型编辑
 * @param data
 */
export function fetchUpdateTeachingPlanText(data: TeachingPlanApi.TeachingPlanTextUpdateInput) {
  return request<void>({
    url: 'AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/TeachingPlanTextUpdate',
    method: 'POST',
    data,
  })
}
