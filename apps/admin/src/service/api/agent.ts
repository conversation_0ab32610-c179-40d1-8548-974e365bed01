import { appApiRequest, request } from '@/service/request'
import { useAuthStore } from '@/store/modules/auth'

/**
 * 获取智能体列表信息
 * @param data 请求参数
 * @returns 返回智能体列表信息
 */
export function agentTeacherHomePage(
  data: Api.Agent.AgentTeacherHomePageListInput,
) {
  return request<Api.Agent.AgentTeacherHomePageListOutPut>({
    url: 'AgentTeacherHomePage/AgentTeacherHomePage/GetAgentListInfo',
    method: 'POST',
    data,
  })
}

/** 获取智能体列表信息 */
export async function fetchTeacherHomePageAgentListInfo(
  body: AgentApi.AgentTeacherHomePageListInput,
) {
  return request<AgentApi.AgentTeacherHomePageListOutPut>({
    url: 'AgentTeacherHomePage/AgentTeacherHomePage/GetAgentListInfo',
    method: 'POST',
    data: body,
  })
}

/**
 * 获取智能体收藏列表
 * @param body 请求参数
 */
export async function teacherHomePageAgentCollection(body: {
  /**
   * 智能体Id
   */
  agentId?: null | string
  /**
   * 教师Id
   */
  teacherId?: null | string
}) {
  return request<void>({
    url: 'AgentTeacherHomePage/AgentTeacherHomePage/AgentCollection',
    method: 'POST',
    data: body,
  })
}

/** 教师保存/编辑口语交际 */
export async function fetchSaveTeacherOralCommunication(
  body: AgentApi.SaveTeacherOralCommunicationInput,
) {
  return request<void>({
    url: 'AgentTeacherOralCommunication/AgentTeacherOralCommunication/SaveTeacherOralCommunication',
    method: 'POST',
    data: body,
  })
}

/**
 * 获取口语交际详情
 */
export async function fetchOralCommunicationDetail(body: {
  /** 智能体任务Id */
  Id?: string
}) {
  return request<AgentApi.AgentOralCommunicationDetailOutput>({
    url: 'AgentTeacherOralCommunication/AgentTeacherOralCommunication/GetOralCommunicationDetail',
    method: 'POST',
    data: body,
  })
}

/**
 * 获取教师授课班级列表
 * @param data 请求参数
 *   @property {string} year - 学年
 *   @property {string} grade - 年级
 *   @property {string} UserId - 教师ID
 * @returns 返回教师授课班级信息数组
 */
export async function fetchGetTeachClass(data: {
  year: string
  grade: string
  UserId: string
}) {
  return appApiRequest<AgentApi.GetTeachClassReponse[]>({
    url: 'Class/TeacherClassManager/GetTeachClass',
    method: 'GET',
    params: data,
  })
}

/**
 * 获取当前学年学期及其列表
 * @returns 返回当前学年学期信息数组
 */
export async function fetchGetNowYearSemesterAndList() {
  return appApiRequest<AgentApi.YearSemesterResponse>({
    url: 'SemesterTime/SemesterTime/GetNowYearSemesterAndList',
    method: 'GET',
  })
}

/**
 * 获取教材章节列表
 * @param data 请求参数
 *   @property {string} year - 学年
 *   @property {string} grade - 年级
 *   @property {string} term - 学期
 * @returns 返回教材章节信息数组
 */
export async function fetchGetChapterListByCondition(data: {
  year: string
  grade: string
  term: string
}) {
  return appApiRequest<AgentApi.ChapterResponse[]>({
    url: 'Chapter/Chapter/GetChapterListByCondition',
    method: 'GET',
    params: data,
    headers: {
      // 学科Id
      subjectid: useAuthStore().activeState.subjectId,
    },
  })
}

/**
 * 获取年级列表
 * @param data 请求参数
 *   @property {string} year - 学年
 * @returns 返回处理后的年级列表数据，其中每个年级的Id会被转换为字符串格式
 */
export async function fetchGetGradeList(data: { year: string }) {
  return appApiRequest<AgentApi.GradeResponse[]>({
    url: 'Class/TeacherClassManager/GetGradeList',
    method: 'GET',
    params: data,
  }).then((res) => {
    if (!res.error) {
      return res.data.map((item) => {
        item.Id = `${item.Id}`
        return item
      })
    }
  })
}

/**
 * 获取口语交际列表
 * @param data 请求参数
 * @returns 返回口语交际列表
 */
export async function fetchGetOralCommunicationList(
  data: AgentApi.OralCommunicationListRequest,
) {
  return request<AgentApi.OralCommunicationListReponse>({
    url: 'AgentTeacherOralCommunication/AgentTeacherOralCommunication/GetOralCommunicationList',
    method: 'POST',
    data,
  })
}

/**
 * 删除口语交际
 * @param data 请求参数
 * @returns 返回删除结果
 */
export async function fetchDelOralCommunication(data: {
  AgentTaskId: string
  TeacherId?: string
}) {
  return request({
    url: 'AgentTeacherOralCommunication/AgentTeacherOralCommunication/DelOralCommunication',
    method: 'POST',
    data,
  })
}

/**
 * 获取AI文件消息
 */
export async function fetchGetAIFileInfoList() {
  return request<AgentApi.AIFileInfoDto[]>({
    url: 'AgentCommon/AgentCommon/GetAIFileInfoList',
    method: 'POST',
    data: {},
  })
}

/**
 * AI生成HTML代码
 * @param data
 */
export async function fetchAIGenerateHTMLCode(data: { Prompt: string }) {
  return request<AgentApi.AIGenerateHTMLCodeOutput>({
    url: 'AgentCommon/AgentCommon/AIGenerateHTMLCode',
    method: 'POST',
    data,
    timeout: 600000,
  })
}

/**
 * AI获取分析结果
 * @param data
 */
export async function fetchGetOralCommunicationAnalyse(data: AgentApi.GetOralCommunicationAnalyseInput) {
  return request<AgentApi.GetOralCommunicationAnalyseOutput>({
    url: 'AgentTeacherOralCommunication/AgentTeacherOralCommunication/GetOralCommunicationAnalyse',
    method: 'POST',
    data,
    timeout: 600000,
  })
}

/**
 * 获取获取AI对话内容记录
 * @param data 请求参数，
 * @returns
 */
export function fetchGetAIDialogueContentRecord(data: AgentApi.AgentStudentOralCommunicationDialogueRequest) {
  return request<AgentApi.AgentTeacherHomePageAgentTaskOuputResponse>({
    url: 'AgentCommon/AgentCommon/GetAIDialogueContentRecord',
    method: 'POST',
    data,
  })
}

/**
 * 获取学生评估结果
 * @param data
 */
export function fetchGetOralCommunicationStudentResult(data: AgentApi.GetOralCommunicationStudentResultInput) {
  return request<AgentApi.GetOralCommunicationStudentResultOutput>({
    url: 'AgentTeacherOralCommunication/AgentTeacherOralCommunication/GetOralCommunicationStudentResult',
    method: 'POST',
    data,
  })
}

/**
 * 获取智能体模型信息
 */
export function fetchGetAgentModelInfo() {
  return request<TeachingPlanApi.GetAgentModelInfoOutput[]>({
    url: 'AgentCommon/AgentCommon/GetAgentModelInfo',
    method: 'POST',
    data: {},
  })
}
