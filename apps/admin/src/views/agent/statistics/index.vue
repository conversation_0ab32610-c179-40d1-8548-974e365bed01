<script setup lang="ts">
import { ref } from 'vue'
import { useRequest, useRouterPush } from '@sa/hooks'
import AgentInfoHeader from './components/agent-info-header.vue'
import SummaryCards from './components/summary-cards.vue'
import GradeDistributionChart from './components/grade-distribution-chart.vue'
import ScoreDistributionTable from './components/score-distribution-table.vue'
import type { ScoreTableItem } from './components/score-distribution-table.vue'
import { useAuthStore } from '@/store/modules/auth'
import { fetchGetOralCommunicationAnalyse } from '@/service/api'

defineOptions({
  name: 'AgentStatistics',
})

const query = useRoute().query

const agentTaskId = query.taskId as string
const agentId = query.agentId as string

const authStore = useAuthStore()

// 标签页数据
const tabOptions = computed(() => {
  return (authStore.userInfo.classList ?? []).map(v => ({
    key: v.ClassId,
    label: v.ClassName,
  }))
})

const activeTab = ref(tabOptions.value[0]?.key)

const { data, run } = useRequest(() => {
  return fetchGetOralCommunicationAnalyse({
    AgentTaskId: agentTaskId as string,
    ClassId: activeTab.value,
  })
}, {
  manual: true,
})

// 智能体信息
const agentInfo = computed(() => {
  return {
    id: data.value?.AgentId ?? '',
    name: data.value?.AgentTaskName ?? '',
    description: data.value?.AgentTaskIntroduce ?? '',
    avatar: data.value?.Logo ?? '',
    category: data.value?.InteractiveMode ?? '',
  }
})

watch(activeTab, () => {
  run()
}, {
  immediate: true,
})

// 综合分析数据
const summaryData = computed(() => {
  return {
    submissionCount: data.value?.SubmitCount ?? 0,
    averageScore: data.value?.AvgLevel ?? '',
  }
})

// 等地分布数据
const gradeDistributionData = computed(() => {
  return (data.value?.LevelList ?? []).map(v => ({
    grade: v.LevelName!,
    count: v.LevelCount!,
    color: ({
      A: '#CEA3FF',
      B: '#CEA3FF',
      C: '#CEA3FF',
      D: '#CEA3FF',
    })[v.LevelName!] ?? '#CDCDCD',
  }))
})

// 成绩分布表格数据
const scoreTableData = computed(() => {
  return (data.value?.ScoreList ?? []).map(v => ({
    studentName: v.StudentName!,
    grade: v.StudentLevel!,
    studentId: v.StudentNum!,
    action: '查看明细',
  }))
})

// 事件处理
function handleBack() {
  // 可以在这里添加自定义的返回逻辑
}

function handleTabChange(value: string) {
  activeTab.value = value
  // 可以在这里添加标签页切换的逻辑，比如重新加载数据
}

const { routerPushByKey } = useRouterPush()

function handleViewDetail(row: ScoreTableItem) {
  //
  routerPushByKey('agent_evaluation', {
    query: {
      taskId: agentTaskId,
      agentId,
      studentId: row.studentId as string,
    },
  })
}
</script>

<template>
  <div class="s:p-12px relative h-full bg-gray-50 p-16px pt-64px dark:bg-gray-900">
    <BackButton />

    <NScrollbar class="h-full">
      <!-- 智能体信息头部 -->
      <NCard class="mb-12px">
        <AgentInfoHeader
          :agent-info="agentInfo"
          title="查看统计"
          @back="handleBack"
        />
      </NCard>

      <!-- 班级选择标签页 -->
      <NCard hoverable class="mb-12px">
        <div class="px-4px py-8px">
          <NTabs
            :value="activeTab"
            animated
            :bar-width="28"
            type="line"
            @update:value="handleTabChange"
          >
            <NTab
              v-for="item in tabOptions"
              :key="item.key"
              :name="item.key"
              :tab="item.label"
            />
          </NTabs>
        </div>
        <!-- 主要内容区域 - 响应式网格布局 -->
        <NGrid
          responsive="screen"
          :x-gap="12"
          :y-gap="12"
          item-responsive
          class="mb-12px"
        >
          <!-- 综合分析统计卡片 - 全宽 -->
          <NGridItem span="24">
            <NCard title="综合分析" hoverable>
              <SummaryCards :summary-data="summaryData" />
            </NCard>
          </NGridItem>

          <!-- 等地分布图表 - 左侧 -->
          <NGridItem span="24 ">
            <NCard title="等级分布" hoverable>
              <GradeDistributionChart
                :grade-distribution-data="gradeDistributionData"
              />
            </NCard>
          </NGridItem>
        </NGrid>
      </NCard>

      <NCard title="成绩分布" hoverable>
        <ScoreDistributionTable
          :score-table-data="scoreTableData"
          @view-detail="handleViewDetail"
        />
      </NCard>
    </NScrollbar>
  </div>
</template>

<style scoped>
/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片包装器样式 */
.card-wrapper {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out;
}

.card-wrapper:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 网格项目动画 */
:deep(.n-grid-item) {
  animation: fadeInUp 0.6s ease-out;
}

:deep(.n-grid-item:nth-child(1)) {
  animation-delay: 0.1s;
}

:deep(.n-grid-item:nth-child(2)) {
  animation-delay: 0.2s;
}

:deep(.n-grid-item:nth-child(3)) {
  animation-delay: 0.3s;
}

/* 标签页样式优化 */
:deep(.n-tabs .n-tabs-nav) {
  background: transparent;
}

:deep(.n-tabs .n-tabs-tab) {
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.n-tabs .n-tabs-tab:hover) {
  color: #3b82f6;
}

:deep(.n-tabs .n-tabs-tab.n-tabs-tab--active) {
  color: #3b82f6;
  font-weight: 600;
}
</style>
