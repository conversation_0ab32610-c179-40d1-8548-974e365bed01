<script setup lang="ts">
import type { StepsProps } from 'naive-ui'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { HuaweiOBSUploader } from '@sa/utils'
import { usePromiseDialog } from '@sa/hooks'
import StepOne from './components/step-one/index.vue'
import StepTwo from './components/step-two/index.vue'
import {
  fetchOralCommunicationDetail,
  fetchSaveTeacherOralCommunication,
} from '@/service/api/agent'

defineOptions({
  name: 'Spoken',
})
const route = useRoute()
const agentId = computed(() => route.query.agentId as string)
const taskId = route.query.taskId as string
const currentRef = ref(1)

const router = useRouter()

const submitting = ref(false)

const detailInfo = ref()

const IsData = ref(false)

// 临时生成的动画列表
const tempAnimList = ref([])

const initRequired = ref({
  one: false,
  two: false,
})

// 子组件引用
const stepOneRef = ref()
const stepTwoRef = ref()

provide('tempAnimList', tempAnimList)

// 动态计算步骤状态
const steps = computed(() => [
  {
    title: '基础任务属性',
    status: (currentRef.value > 1
      ? 'finish'
      : currentRef.value === 1
        ? 'process'
        : 'wait') as StepsProps['status'],
  },
  {
    title: '场景与互动规则',
    status: (currentRef.value > 2
      ? 'finish'
      : currentRef.value === 2
        ? 'process'
        : 'wait') as StepsProps['status'],
  },
  {
    title: '完成',
    status: (currentRef.value === 3
      ? 'finish'
      : 'wait') as StepsProps['status'],
  },
])

// 动态组件映射
const currentComponent = computed(() => {
  switch (currentRef.value) {
    case 1:
      return StepOne
    case 2:
      return StepTwo
    case 3:
      return null // 完成页面不需要组件
    default:
      return StepOne
  }
})

// 获取当前步骤的组件引用
function getCurrentStepRef() {
  switch (currentRef.value) {
    case 1:
      return stepOneRef.value
    case 2:
      return stepTwoRef.value
    default:
      return null
  }
}

function resetScroll() {
  // 尝试多种可能的滚动容器
  const scrollContainers = [
    '.n-scrollbar-container',
    '.n-layout-scroll-container',
    '#__SCROLL_EL_ID__',
    '.n-layout-content',
    'main',
  ]

  for (const selector of scrollContainers) {
    const el = document.querySelector(selector)
    if (el) {
      el.scrollTo({ left: 0, top: 0, behavior: 'smooth' })
      break
    }
  }

  // 如果没有找到特定的滚动容器，滚动到页面顶部
  window.scrollTo({ left: 0, top: 0, behavior: 'smooth' })
}

async function uploadTextToFile(
  formData: AgentApi.SaveTeacherOralCommunicationInput,
) {
  return new Promise((resolve, reject) => {
    // 需要遍历 formData 并处理 HtmlFileInfo
    if (formData.InteractiveMode === 1) {
      const uploader = new HuaweiOBSUploader()
      const promises: Promise<void>[] = []
      formData.InstructTasks!.forEach((task) => {
        if (!task.HtmlFileInfo?._isHtmlCode) {
          return
        }
        const blob = new Blob([task.HtmlFileInfo!.Url!], { type: 'text/html' })
        const file = new File([blob], `${task.Name}.html`, { type: blob.type })
        // 文本上传到obs
        const p1 = uploader
          .upload({
            file,
          })
          .then((res) => {
            task.HtmlFileInfo!.Url = res.url
            const key = res.key
            // 设置元数据
            return uploader.setObjectMetadata({
              key,
              contentDisposition: 'inline',
            })
          })

        promises.push(p1)
      })

      Promise.all(promises)
        .then(() => {
          resolve(true)
        })
        .catch((e) => {
          console.error('文本上传失败:', e)
          reject(new Error('文本上传失败'))
        })

      return
    }
    resolve(true)
  })
}

// 保存口语交际任务
async function saveTeacherOralCommunication(
  formData: AgentApi.SaveTeacherOralCommunicationInput,
) {
  try {
    submitting.value = true

    await uploadTextToFile(formData)

    if (formData.InteractiveMode === 1) {
      formData.DialogueTasks = []
    }

    if (formData.InteractiveMode === 2) {
      formData.InstructTasks = []
    }

    const { error } = await fetchSaveTeacherOralCommunication(formData)
    if (!error) {
      window.$message?.success('任务创建成功')
      return true
    }
    throw error
  }
  catch (error) {
    console.error('保存失败:', error)
    window.$message?.error('保存失败，请重试')
    return false
  }
  finally {
    submitting.value = false
  }
}

const { showPromiseDialog } = usePromiseDialog()

async function checkIsData() {
  if (IsData.value) {
    await showPromiseDialog({
      content: '当前已有学生问答数据，确认后，问答数据清空，学生需要重新进行问答',
    })
  }
  else {
    return false
  }
}

async function next() {
  // 如果不是最后一步，需要校验当前步骤的表单
  if (currentRef.value < 3) {
    const currentStepRef = getCurrentStepRef()
    // 如果当前步骤有组件引用，尝试校验表单
    if (currentStepRef && currentStepRef.validate) {
      try {
        await currentStepRef.validate()

        // 如果是第二步完成（点击完成按钮），收集所有表单数据并保存
        if (currentRef.value === 2) {
          const stepOneData = stepOneRef.value?.formModel
          const stepTwoData = stepTwoRef.value?.formModel
          // 合并所有表单数据
          const allFormData = {
            AgentId: agentId.value,
            ...stepOneData,
            ...stepTwoData,
          }

          // 检验是否已答数据
          await checkIsData()

          // 调用保存接口
          const success = await saveTeacherOralCommunication(allFormData)
          if (!success) {
            return // 保存失败，不进入下一步
          }

          router.back()

          return
        }

        // 校验通过，进入下一步
        currentRef.value++

        nextTick(() => {
          shouldInitData()
        })

        resetScroll()
      }
      catch (error) {
        // 校验失败，显示错误信息
        window.$message?.error('请完善当前步骤的必填信息')
        console.error('表单校验失败:', error)
      }
    }
    else {
      // 如果没有校验方法，直接进入下一步
      currentRef.value++
      resetScroll()
    }
  }
}

function prev() {
  if (currentRef.value > 1) {
    currentRef.value--
    resetScroll()
  }
}

async function fetchDetail() {
  if (!taskId) {
    return
  }
  const { data, error } = await fetchOralCommunicationDetail({
    Id: taskId,
  })
  if (error) {
    return
  }

  if (!data?.OralCommunicationInfo) {
    return
  }

  IsData.value = !!data.IsData

  const info = data.OralCommunicationInfo

  Object.keys(info).forEach((key) => {
    if (key === 'TimeRange') {
      info[key] = [
        dayjs(info![key]![0]).format('YYYY-MM-DD HH:mm:ss'),
        dayjs(info![key]![1]).format('YYYY-MM-DD HH:mm:ss'),
      ]
    }
  })

  detailInfo.value = info

  initRequired.value.one = true
  initRequired.value.two = true

  shouldInitData()
}

function shouldInitData() {
  if (stepOneRef.value && initRequired.value.one) {
    stepOneRef.value?.initData(detailInfo.value)
    initRequired.value.one = false
  }

  if (stepTwoRef.value && initRequired.value.two) {
    stepTwoRef.value?.initData(detailInfo.value)
    initRequired.value.two = false
  }
}

onMounted(() => {
  fetchDetail()
})

function goBack() {
  showPromiseDialog(
    {
      content: '是否确认返回？',
    },
  ).then(() => {
    router.back()
  })
}
</script>

<template>
  <div class="flex-col-stretch gap-16px lt-sm:overflow-auto">
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper">
      <!-- 固定的步骤条 -->
      <div class="h-100px flex flex-shrink-0 justify-center">
        <NSteps
          :current="currentRef"
          class="mb-24px w-90% flex items-center justify-center"
        >
          <NStep
            v-for="(step, index) in steps"
            :key="index"
            :title="step.title"
            :status="step.status"
          />
        </NSteps>
      </div>
      <KeepAlive v-if="currentComponent">
        <component
          :is="currentComponent"
          :ref="
            (el) => {
              if (currentRef === 1) {
                stepOneRef = el;
              }
              else if (currentRef === 2) {
                stepTwoRef = el;
              }
            }
          "
        />
      </KeepAlive>

      <NResult
        v-else-if="currentRef === 3"
        status="success"
        title="发布成功"
        description="任务已成功创建并发布"
        class="mb-24px"
      />

      <!-- 固定的导航按钮 -->
      <div class="flex justify-center gap-12px bg-white p-b-20px">
        <NButton v-if="currentRef === 1" @click="goBack">
          返回
        </NButton>
        <NButton v-if="currentRef > 1" :disabled="currentRef === 1" @click="prev">
          上一步
        </NButton>
        <NButton
          type="primary"
          :disabled="currentRef === 3"
          :loading="submitting"
          @click="next"
        >
          {{ currentRef === 2 ? "完成并发布" : "下一步" }}
        </NButton>
      </div>
    </NCard>
  </div>
</template>
