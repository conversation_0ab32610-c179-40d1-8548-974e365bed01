<script setup lang="ts" name="AiGenImageModal">
import { fetchAIGenerateImage } from '@/service/api/common'
import { useAppStore } from '@/store/modules/app'

interface Modal {
  show: boolean
}

interface Emit {
  insert: [img: string]
}

const emit = defineEmits<Emit>()

const modal = defineModel<Modal>('modal')

const appStore = useAppStore()

// 1 等待生成 2 生成中 3 生成结果
const status = ref<1 | 2 | 3>(1)

const prompt = ref('')
const resultImg = ref('')

watch(
  () => modal.value!.show,
  () => {
    status.value = 1
    resultImg.value = ''
    prompt.value = ''
  },
)

async function startGenImage() {
  if (appStore.aiGenCount >= 3) {
    window.$message?.warning('您已达到最大生成次数')
    return
  }
  if (!prompt.value) {
    window.$message!.warning('请输入提示词')
    return
  }
  status.value = 2
  const { error, data } = await fetchAIGenerateImage({
    Prompt: prompt.value,
  })
  if (error) {
    status.value = 1
    return
  }
  appStore.aiGenCount += 1 // 记录AI生成次数
  resultImg.value = data
  status.value = 3
}

function onTap() {
  if (status.value === 1) {
    startGenImage()
    return
  }

  if (status.value === 2) {
    status.value = 1
    return
  }

  if (status.value === 3) {
    if (!resultImg.value) {
      window.$message?.error('请先生成图片')
      return
    }
    emit('insert', resultImg.value)
  }
}

function reGenImage() {
  startGenImage()
}
</script>

<template>
  <NModal
    v-model:show="modal!.show"
    title="AI生成图片"
    preset="card"
    class="w-50vw"
  >
    <NInput
      v-model:value="prompt"
      type="textarea"
      :rows="5"
      placeholder="请描述您想要生成的图片"
    />

    <div>
      <NSpin :show="status === 2">
        <div
          class="my-24px h-200px w-full flex flex-col items-center justify-center border-1 rounded-8px border-dashed text-24 text-gray"
        >
          <template v-if="[1, 2].includes(status)">
            <icon-local-image-outline />
            <span class="text-20px">
              {{
                status === 1
                  ? "您生成的图片将在这里展示"
                  : "AI正在加紧生成中，请稍后..."
              }}
            </span>
          </template>

          <template v-if="[3].includes(status)">
            <NImage :src="resultImg" class="h-full" />
          </template>
        </div>
      </NSpin>
    </div>

    <div class="flex items-center justify-center gap-x-16px">
      <NButton v-if="status === 3" @click="reGenImage">
        重新生成
      </NButton>
      <NButton type="primary" @click="onTap">
        {{
          {
            1: "开始生成",
            2: "取消生成",
            3: "插入图片",
          }[status]
        }}
      </NButton>
    </div>
  </NModal>
</template>

<style scoped></style>
