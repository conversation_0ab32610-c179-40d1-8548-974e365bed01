<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { NButton, NForm, NFormItem, NInput, NModal, NSpace } from 'naive-ui'

interface Props {
  show: boolean
  teachingPlan?: TeachingPlanApi.GetTeachingPlanOutput | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'confirm', data: { id: string, name: string }): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  name: '',
})

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入教案名称',
    trigger: 'blur',
  },
}

// 表单引用
const formRef = ref()

// 监听弹窗显示状态和教案数据，初始化表单
watch([() => props.show, () => props.teachingPlan], ([newShow, newTeachingPlan]) => {
  if (newShow && newTeachingPlan) {
    formData.name = newTeachingPlan.Name || ''
    formRef.value?.restoreValidation()
  }
})

// 关闭弹窗
function handleClose() {
  emit('update:show', false)
}

// 取消操作
function handleCancel() {
  emit('cancel')
  handleClose()
}

// 确认操作
function handleConfirm() {
  formRef.value?.validate((errors: any) => {
    if (!errors && props.teachingPlan) {
      const trimmedName = formData.name.trim()

      // 检查名称是否有变化
      if (trimmedName === props.teachingPlan.Name) {
        window.$message?.info('教案名称未发生变化')
        return
      }

      emit('confirm', {
        id: props.teachingPlan.Id!,
        name: trimmedName,
      })
      handleClose()
    }
  })
}

// 处理键盘事件
function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
    handleConfirm()
  }
  else if (e.key === 'Escape') {
    handleCancel()
  }
}

// 处理输入框回车事件
function handleInputEnter() {
  handleConfirm()
}
</script>

<template>
  <NModal
    :show="show"
    :mask-closable="false"
    preset="card"
    title="修改教案名称"
    class="edit-teaching-plan-modal"
    :style="{ width: '450px' }"
    @update:show="handleClose"
    @keydown="handleKeydown"
  >
    <div class="modal-content">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="top"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <!-- 教案名称 -->
        <NFormItem label="教案名称" path="name">
          <NInput
            v-model:value="formData.name"
            placeholder="请输入教案名称"
            maxlength="100"
            show-count
            clearable
            autofocus
            @keydown.enter="handleInputEnter"
          />
        </NFormItem>
      </NForm>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="modal-footer">
        <NSpace justify="end">
          <NButton @click="handleCancel">
            取消
          </NButton>
          <NButton
            type="primary"
            @click="handleConfirm"
          >
            确定
          </NButton>
        </NSpace>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
.edit-teaching-plan-modal {
  border-radius: 8px;
}

.modal-content {
  padding: 8px 0;
}

.modal-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.n-card-header) {
  padding: 20px 24px 16px;
  font-size: 16px;
  font-weight: 500;
}

:deep(.n-card__content) {
  padding: 0 24px 20px;
}

:deep(.n-card__footer) {
  padding: 0 24px 20px;
}

:deep(.n-form-item-label) {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 4px;
}

:deep(.n-input__input-el) {
  font-size: 14px;
}

:deep(.n-button) {
  border-radius: 4px;
  font-size: 14px;
}
</style>
