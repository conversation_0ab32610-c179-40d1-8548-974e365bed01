<script setup lang="tsx">
import { ref } from 'vue'
import { N<PERSON><PERSON>on, NCard, NDataTable, NInput, NPopconfirm, NTag } from 'naive-ui'
import { useRouterPush, useTable } from '@sa/hooks'
import dayjs from 'dayjs'
import AddTeachingPlanModal from './components/AddTeachingPlanModal.vue'
import EditTeachingPlanModal from './components/EditTeachingPlanModal.vue'
import {
  fetchDelTeachingPlan,
  fetchGetTeachingPlan,
  fetchSaveTeachingPlan,
  fetchTeachingPlanCreateRecordUpdateName,
} from '@/service/api/teaching-plan'

const iconMap: Record<string, () => VNode> = {
  1: () => (
    <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 2C0 0.89543 0.895431 0 2 0L16.9412 0L22.5882 6.17647V22C22.5882 23.1046 21.6928 24 20.5882 24H2C0.895428 24 0 23.1046 0 22V2Z"
        fill="#00D5C5"
      />
      <path
        d="M6.716 7.624H8.12L8.264 6.508H9.32L9.176 7.624H10.232L10.124 8.428C10.34 8.1 10.544 7.7 10.736 7.228H11.816C11.496 8.052 11.06 8.816 10.508 9.52H11.408L11.3 10.516H9.62C9.316 10.812 9.08 11.028 8.912 11.164H9.944C10.2 11.156 10.392 11.204 10.52 11.308C10.656 11.404 10.724 11.532 10.724 11.692C10.724 11.884 10.636 12.068 10.46 12.244L9.248 13.3C9.904 13.212 10.484 13.116 10.988 13.012L10.856 14.032C10.456 14.104 9.872 14.188 9.104 14.284L8.912 15.988C8.864 16.276 8.756 16.488 8.588 16.624C8.428 16.76 8.216 16.816 7.952 16.792C7.424 16.752 6.908 16.632 6.404 16.432L6.536 15.424C6.992 15.616 7.372 15.732 7.676 15.772H7.724C7.78 15.772 7.82 15.76 7.844 15.736C7.868 15.712 7.888 15.66 7.904 15.58L8.036 14.404L6.92 14.524L5.876 14.62L5.996 13.636C6.484 13.604 7.204 13.54 8.156 13.444L8.204 13.072L9.296 12.1H7.712C7.248 12.428 6.78 12.716 6.308 12.964L5.456 12.28C6.032 12 6.536 11.724 6.968 11.452C7.4 11.172 7.82 10.86 8.228 10.516H6.044L6.164 9.52H7.868L8 8.56H6.608L6.716 7.624ZM15.68 9.028C15.36 10.748 14.832 12.24 14.096 13.504C14.656 14.56 15.364 15.568 16.22 16.528L15.02 16.864C14.348 15.944 13.824 15.152 13.448 14.488C12.8 15.4 12.008 16.204 11.072 16.9L9.98 16.432C11.212 15.544 12.192 14.536 12.92 13.408C12.616 12.72 12.372 12.036 12.188 11.356L11.936 11.92H10.808C11.68 10.016 12.36 8.244 12.848 6.604H13.964C13.876 6.9 13.728 7.36 13.52 7.984H16.4L16.268 9.028H15.68ZM9.26 9.52C9.556 9.216 9.816 8.896 10.04 8.56H9.056L8.936 9.52H9.26ZM12.824 9.844C13.024 10.764 13.264 11.58 13.544 12.292C14.024 11.308 14.38 10.22 14.612 9.028H13.148L12.824 9.844Z"
        fill="white"
      />
    </svg>
  ),
  2: () => (
    <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 2C0 0.89543 0.895431 0 2 0L16.9412 0L22.5882 6.17647V22C22.5882 23.1046 21.6928 24 20.5882 24H2C0.895428 24 0 23.1046 0 22V2Z"
        fill="#44BBFF"
      />
      <path
        d="M7.5797 16.432C7.3797 16.432 7.2117 16.368 7.0757 16.24C6.9477 16.12 6.8677 15.928 6.8357 15.664L5.9597 7.42H7.4597L8.2157 15.112H8.5397L11.1077 8.152H13.0037L13.8557 15.124H14.1797L16.8317 7.432H18.3797L15.4517 15.784C15.2997 16.208 15.0317 16.428 14.6477 16.444H13.2317C13.0317 16.444 12.8637 16.38 12.7277 16.252C12.5997 16.132 12.5197 15.94 12.4877 15.676L11.8637 9.988L9.7997 15.772C9.6397 16.212 9.3597 16.432 8.9597 16.432H7.5797Z"
        fill="white"
      />
    </svg>
  ),
}

// 使用 useTable hook
const {
  columns,
  data,
  loading,
  pagination,
  getDataByPage,
  searchParams,
} = useTable({
  apiFn: fetchGetTeachingPlan,
  apiParams: {
    current: 1,
    size: 10,
  },
  columns: () => [
    {
      key: 'Name',
      title: '文件名',
      align: 'left',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
      render: (row: TeachingPlanApi.GetTeachingPlanOutput) => {
        return (
          <div class="flex items-center gap-x-12px">
            {iconMap[row.Type!]?.()}
            <span>{row.Name}</span>
          </div>
        )
      },
    },
    {
      key: 'Type',
      title: '类型',
      align: 'center',
      width: 100,
      render: (row: TeachingPlanApi.GetTeachingPlanOutput) => {
        const typeMap = {
          1: { label: '文本', color: 'info' },
          2: { label: 'Word', color: 'success' },
        }
        const type = typeMap[row.Type as keyof typeof typeMap] || { label: '未知', color: 'default' }
        return <NTag type={type.color as any}>{type.label}</NTag>
      },
    },
    {
      key: 'ModifyTime',
      title: '更新时间',
      align: 'center',
      width: 180,
      render: (row: TeachingPlanApi.GetTeachingPlanOutput) => {
        return row.ModifyTime ? dayjs(row.ModifyTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 150,
      fixed: 'right',
      render: (row: TeachingPlanApi.GetTeachingPlanOutput) => (
        <div class="flex-center justify-end gap-8px">
          <NButton
            type="primary"
            ghost
            secondary
            onClick={() => handleEdit(row)}
          >
            编辑
          </NButton>
          <NButton
            type="primary"
            ghost
            secondary
            onClick={() => handleRename(row)}
          >
            重命名
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.Id!)}>
            {{
              default: () => `确定要删除教案"${row.Name}"吗？此操作不可恢复。`,
              trigger: () => (
                <NButton
                  type="error"
                  ghost
                  secondary
                  size="small"
                >
                  删除
                </NButton>
              ),
            }}
          </NPopconfirm>
        </div>
      ),
    },
  ] as unknown as any,
})

// 搜索关键词
const searchKeyword = ref('')

// 添加教案弹窗状态
const showAddModal = ref(false)

// 编辑教案弹窗状态
const showEditModal = ref(false)
const currentEditingPlan = ref<TeachingPlanApi.GetTeachingPlanOutput | null>(null)

// 搜索处理
function handleSearch() {
  Object.assign(searchParams, {
    PageIndex: 1,
  })
  // 如果有搜索关键词，可以在这里处理
  if (searchKeyword.value) {
    // 这里可以根据实际API需求添加搜索参数
    console.log('搜索关键词:', searchKeyword.value)
  }
  getDataByPage()
}
const { routerPushByKey } = useRouterPush()

// 添加教案
function handleAddTeachingPlan() {
  showAddModal.value = true
}

// AI教案入口
function handleAITeachingPlan() {
  console.log('AI教案')
  routerPushByKey('teaching-plan_home')
}

// 操作处理函数
function handleRename(row: TeachingPlanApi.GetTeachingPlanOutput) {
  currentEditingPlan.value = row
  showEditModal.value = true
}

function handleEdit(row: TeachingPlanApi.GetTeachingPlanOutput) {
  // 编辑
  routerPushByKey('teaching-plan-records_edit', {
    query: {
      recordId: row.Id as string,
      fileId: row.FileId as string,
    },
  })
}

async function handleDelete(id: string) {
  const { error } = await fetchDelTeachingPlan({ Id: id })
  if (error) {
    return
  }
  window.$message?.success('教案删除成功')
  getDataByPage()
}

// 添加教案弹窗确认
async function handleAddConfirm(data: { name: string, fileUrl: string }) {
  const { error } = await fetchSaveTeachingPlan({
    Type: 2,
    Name: data.name,
    FileUrl: data.fileUrl,
  })
  if (error) {
    return
  }
  // 这里可以调用添加教案的API
  window.$message?.success('教案添加成功')
  // 刷新列表
  getDataByPage()
}

// 添加教案弹窗取消
function handleAddCancel() {
  console.log('取消添加教案')
}

// 编辑教案弹窗确认
async function handleEditConfirm(data: { id: string, name: string }) {
  console.log('修改教案名称:', data)
  const { error } = await fetchTeachingPlanCreateRecordUpdateName({
    Id: data.id,
    Name: data.name,
  })
  if (error) {
    return
  }
  // 这里可以调用修改教案名称的API
  window.$message?.success('教案名称修改成功')
  // 刷新列表
  getDataByPage()
}

// 编辑教案弹窗取消
function handleEditCancel() {
  console.log('取消修改教案名称')
  currentEditingPlan.value = null
}
</script>

<template>
  <div class="h-full flex flex-col gap-4 p-4">
    <!-- 头部操作区域 -->
    <div class="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <!-- 左侧添加教案按钮 -->
      <NButton
        type="primary"
        class="add-teaching-plan-btn self-start"
        round
        @click="handleAddTeachingPlan"
      >
        <template #icon>
          <icon-ic-round-add class="text-icon" />
        </template>
        添加教案
      </NButton>

      <!-- 右侧操作区域 -->
      <div class="flex flex-col items-stretch gap-3 sm:flex-row sm:items-center">
        <!-- AI教案入口按钮 -->
        <NButton class="ai-teaching-plan-btn" round @click="handleAITeachingPlan">
          <img class="mr-4px w-22px" src="@/assets/imgs/teaching-plan/ai-logo.png" alt="">
          AI教案
        </NButton>

        <!-- 搜索框 -->
        <NInput
          v-model:value="searchKeyword"
          placeholder="请输入搜索内容"
          class="w-full rounded-16px sm:w-300px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #suffix>
            <div class="search-icon-wrapper" @click="handleSearch">
              <icon-ic-round-search class="search-icon" />
            </div>
          </template>
        </NInput>
      </div>
    </div>

    <!-- 表格区域 -->
    <NCard class="flex flex-col flex-1">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">教案列表</span>
          <span class="text-sm text-gray-500">共 {{ pagination.itemCount }} 条记录</span>
        </div>
      </template>

      <div class="flex flex-col flex-1">
        <NDataTable
          :columns="columns"
          :data="data"
          :loading="loading"
          :scroll-x="1000"
          :pagination="pagination"
          :row-key="(row: any) => row.Id"

          remote
          striped
          class="flex-1"
        />
      </div>
    </NCard>

    <!-- 添加教案弹窗 -->
    <AddTeachingPlanModal
      v-model:show="showAddModal"
      @confirm="handleAddConfirm"
      @cancel="handleAddCancel"
    />

    <!-- 编辑教案弹窗 -->
    <EditTeachingPlanModal
      v-model:show="showEditModal"
      :teaching-plan="currentEditingPlan"
      @confirm="handleEditConfirm"
      @cancel="handleEditCancel"
    />
  </div>
</template>

<style scoped>
/* 添加教案按钮样式 */
.add-teaching-plan-btn {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border: none;
  border-radius: 20px;
  padding: 0 20px;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
  transition: all 0.3s ease;
}

.add-teaching-plan-btn:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
  transform: translateY(-1px);
}

.add-teaching-plan-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(96, 165, 250, 0.3);
}

/* AI教案按钮样式 */
.ai-teaching-plan-btn {
  background: linear-gradient(135deg, #bfdbfe 0%, #ddd6fe 100%);
  border: none;
  border-radius: 20px;
  padding: 0 16px;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  transition: all 0.3s ease;
}

.ai-teaching-plan-btn:hover {
  background: linear-gradient(135deg, #93c5fd 0%, #c4b5fd 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(191, 219, 254, 0.4);
}

.ai-teaching-plan-btn:active {
  transform: translateY(0);
}

/* AI图标样式 */
.ai-icon {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2px;
}

.ai-text {
  color: white;
  font-size: 10px;
  font-weight: 700;
  line-height: 1;
}

/* 搜索框样式 */
.search-input {
  border-radius: 20px;
}

.search-icon-wrapper {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.search-icon-wrapper:hover {
  background-color: #f3f4f6;
}

.search-icon {
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

/* 覆盖 NButton 的默认样式 */
:deep(.add-teaching-plan-btn.n-button) {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%) !important;
  border: none !important;
}

:deep(.add-teaching-plan-btn.n-button:hover) {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
}

:deep(.add-teaching-plan-btn.n-button:focus) {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%) !important;
}

/* 覆盖 AI教案按钮的默认样式 */
:deep(.ai-teaching-plan-btn.n-button) {
  background: linear-gradient(135deg, #bfdbfe 0%, #ddd6fe 100%) !important;
  border: none !important;
  color: #374151 !important;
}

:deep(.ai-teaching-plan-btn.n-button:hover) {
  background: linear-gradient(135deg, #93c5fd 0%, #c4b5fd 100%) !important;
  color: #374151 !important;
}

:deep(.ai-teaching-plan-btn.n-button:focus) {
  background: linear-gradient(135deg, #bfdbfe 0%, #ddd6fe 100%) !important;
  color: #374151 !important;
}

/* 覆盖搜索框的默认样式 */
:deep(.search-input .n-input__input-el) {
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  padding: 8px 16px;
  font-size: 14px;
  color: #374151;
}

:deep(.search-input .n-input__input-el::placeholder) {
  color: #9ca3af;
}

:deep(.search-input .n-input-wrapper) {
  border-radius: 20px;
}

:deep(.search-input .n-input__border) {
  border-radius: 20px;
}

:deep(.search-input .n-input__state-border) {
  border-radius: 20px;
}

:deep(.search-input:hover .n-input__state-border) {
  border-color: #d1d5db;
}

:deep(.search-input.n-input--focus .n-input__state-border) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}
</style>
