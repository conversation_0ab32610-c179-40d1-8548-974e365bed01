<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { NButton, NForm, NFormItem, NInput, NModal, NSpace } from 'naive-ui'
import FileUpload from '@sa/components/common/file-upload.vue'
import type { FormRules } from 'naive-ui'

interface Props {
  show: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'confirm', data: { name: string, fileUrl: string }): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  name: '',
  fileList: [] as string[],
})

// 表单验证规则
const rules: FormRules = {
  name: [
    {
      required: true,
      message: '请输入教案名称',
      trigger: 'blur',
    },
  ],
  fileList: [
    {
      required: true,
      type: 'array',
      min: 1,
      message: '请上传教案文件',
      trigger: 'change',
    },
  ],
}

// 表单引用
const formRef = ref()

// 监听弹窗显示状态，重置表单
watch(() => props.show, (newShow) => {
  if (newShow) {
    resetForm()
  }
})

// 重置表单
function resetForm() {
  formData.name = ''
  formData.fileList = []
  formRef.value?.restoreValidation()
}

// 关闭弹窗
function handleClose() {
  emit('update:show', false)
}

// 取消操作
function handleCancel() {
  emit('cancel')
  handleClose()
}

// 确认操作
function handleConfirm() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      emit('confirm', {
        name: formData.name.trim(),
        fileUrl: formData.fileList[0],
      })
      handleClose()
    }
  })
}
</script>

<template>
  <NModal
    :show="show"
    :mask-closable="false"
    preset="card"
    title="添加教案"
    class="add-teaching-plan-modal"
    :close-on-esc="false"
    :style="{ width: '500px' }"
    @update:show="handleClose"
  >
    <div class="modal-content">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="top"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <!-- 教案名称 -->
        <NFormItem label="教案名称" path="name">
          <NInput
            v-model:value="formData.name"
            placeholder="请输入教案名称"
            maxlength="100"
            show-count
            clearable
          />
        </NFormItem>

        <!-- 教案文件上传 -->
        <NFormItem label="教案文件" path="fileList">
          <FileUpload
            v-model="formData.fileList"
            :max="1"
            :multiple="false"
            accept=".pdf,.doc,.docx,.txt"
            list-type="image"
          >
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <IconLocalArchiveLinear />
              </n-icon>
            </div>
            <n-text style="font-size: 16px">
              点击或者拖动文件到该区域来上传，支持.pdf,.doc,.docx,.txt等文件
            </n-text>
          </FileUpload>
        </NFormItem>
      </NForm>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="modal-footer">
        <NSpace justify="end">
          <NButton @click="handleCancel">
            取消
          </NButton>
          <NButton
            type="primary"
            @click="handleConfirm"
          >
            确定
          </NButton>
        </NSpace>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
.add-teaching-plan-modal {
  border-radius: 8px;
}

.modal-content {
  padding: 8px 0;
}

.modal-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

:deep(.n-card-header) {
  padding: 20px 24px 16px;
  font-size: 16px;
  font-weight: 500;
}

:deep(.n-card__content) {
  padding: 0 24px 20px;
}

:deep(.n-card__footer) {
  padding: 0 24px 20px;
}

:deep(.n-form-item-label) {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

:deep(.n-input) {
  border-radius: 4px;
}

:deep(.n-input__input-el) {
  font-size: 14px;
}

:deep(.n-button) {
  border-radius: 4px;
  font-size: 14px;
}

:deep(.n-upload) {
  width: 100%;
}

:deep(.n-upload-trigger) {
  width: 100%;
}
</style>
