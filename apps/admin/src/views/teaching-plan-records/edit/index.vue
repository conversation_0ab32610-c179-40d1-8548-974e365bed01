<script setup lang="ts">
import { useRequest } from '@sa/hooks'
import MDEditor, { type ActionType } from '@sa/components/common/md-editor/MDEditor.vue'
import {
  fetchGetTeachingPlanDetails,
  fetchTeachingPlanCreateRecordUpdateName,
  fetchUpdateTeachingPlanText,
} from '@/service/api/teaching-plan'
// eslint-disable-next-line ts/ban-ts-comment
// @ts-expect-error
import webOffice from '@/plugins/web-office-sdk-solution-v2.0.7.es.js'
import { useAuthStore } from '@/store/modules/auth'
import type { IWebOfficeSDK } from '@/typings/web-office-sdk-solution-v2.0.7.es'
import OptimizeModal from '@/views/teaching-plan/create/components/optimize-modal/index.vue'

const recordId = useRoute().query.recordId as string
const fileId = useRoute().query.fileId as string

const { data, run } = useRequest(() => {
  return fetchGetTeachingPlanDetails({
    Id: recordId,
  })
}, {
  manual: true,
})

const editState = ref({
  isEditing: false,
  tempName: '',
})

const { token } = useAuthStore()

const containerRef = ref()

function initWps() {
  const webSdk = webOffice as IWebOfficeSDK
  webSdk.init({
    officeType: 'w',
    appId: import.meta.env.VITE_WPS_APP_ID,
    fileId,
    token,
    mode: 'nomal',
    mount: containerRef.value,
    attrAllow: [],
  })
}

onMounted(() => {
  run()
    .then(() => {
      if (data.value?.Type === 2) {
        initWps()
      }
    })
})

const optimizeModal = ref({
  show: false,
  action: '',
  optimizeText: '',
  recordId: '',
  range: {
    from: 0,
    to: 0,
  },
})

const editorRef = ref<InstanceType<typeof MDEditor>>()

function onEditorAction(action: 'prefect' | 'expand' | 'reduce', options: { text: string, coords: ActionType }) {
  optimizeModal.value.action = (
    ({
      prefect: '润色',
      expand: '扩写',
      reduce: '精简',
    })[action]
  )
  const selection = editorRef.value?.getSelection()
  if (!selection) {
    return
  }
  optimizeModal.value.range = {
    from: selection.from,
    to: selection.to,
  }
  optimizeModal.value.optimizeText = options.text
  optimizeModal.value.recordId = recordId
  optimizeModal.value.show = true
}

async function savePlanName() {
  if (!editState.value.tempName) {
    window.$message?.warning('教案名称不能为空')
    return
  }
  const { error } = await fetchTeachingPlanCreateRecordUpdateName({
    Id: recordId,
    Name: editState.value.tempName,
  })
  if (error) {
    return
  }
  editState.value.isEditing = !editState.value.isEditing
  data.value!.Name = editState.value.tempName
  window.$message?.success('教案名称修改成功')
}

async function onSavePlan() {
  if (!data.value) {
    return
  }
  const { error } = await fetchUpdateTeachingPlanText({
    Id: recordId,
    Name: data.value.Name,
    Text: data.value.Text,
  })
  if (error) {
    return
  }
  window.$message?.success('教案保存成功')
}
</script>

<template>
  <div class="relative size-full bg-gray-500 !pt-64px">
    <back-button />
    <template v-if="data?.Type === 2">
      <div ref="containerRef" class="size-full" />
    </template>
    <!-- 普通markdown编辑 -->
    <template v-else-if="data?.Type === 1">
      <div
        v-if="data"
        class="size-full flex flex-col rounded-16px bg-white"
      >
        <div class="min-h-80px flex shrink-0 items-center border-b-1 border-gray-200 px-32px py-16px">
          <img class="mr-8px size-24px shrink-0" src="@/assets/imgs/teaching-plan/file-word.png" alt="">
          <div class="flex flex-1 items-center">
            <template v-if="!editState.isEditing">
              <span class="line-clamp-1 text-[#6E6F70]">{{ data.Name }}</span>
              <img
                class="ml-12px size-20px cursor-pointer"
                src="@/assets/imgs/teaching-plan/ic_edit.png"
                alt=""
                @click="() => {
                  editState.isEditing = !editState.isEditing
                  editState.tempName = data!.Name ?? ''
                }"
              >
            </template>
            <template v-else>
              <NInput v-model:value.trim="editState.tempName" class="text-[#6E6F70] !w-200px" />
              <div>
                <NButton
                  quaternary
                  type="info"
                  @click="() => {
                    savePlanName()
                  }"
                >
                  确定
                </NButton>
                <NButton
                  quaternary
                  type="info"
                  @click="editState.isEditing = !editState.isEditing"
                >
                  取消
                </NButton>
              </div>
            </template>
          </div>
          <NButton
            size="large"
            class="text-white !bg-[linear-gradient(90deg,_#A99FFF_0%,_#CFA3FF_100%)]"
            round
            @click="onSavePlan"
          >
            保存
          </NButton>
        </div>
        <div class="min-h-0 flex-1">
          <NScrollbar class="h-full">
            <MDEditor
              ref="editorRef"
              v-model:value="data.Text"
              :show-action="false"
              @on-action="onEditorAction"
            />
          </NScrollbar>
        </div>
      </div>

      <OptimizeModal
        v-model:show="optimizeModal.show"
        :record-id="optimizeModal.recordId"
        :optimize-text="optimizeModal.optimizeText"
        :action="optimizeModal.action"
        @replace="e => {
          editorRef?.replaceRange(e, optimizeModal.range)
        }"
        @insert="e => {
          editorRef?.insert(e, false)
        }"
      />
    </template>
  </div>
</template>

<style scoped>

</style>
