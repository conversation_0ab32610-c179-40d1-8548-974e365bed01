<script setup lang="ts">
import { CreatorType, DocmeeUI } from '@docmee/sdk-ui'
import dayjs from 'dayjs'
import { useAuthStore } from '@/store/modules/auth'
import { fetchTeachingPlanCreateRecordDetails } from '@/service/api/teaching-plan'

const id = useRoute().query.id as string
const expires = useRoute().query.expires as string

const { userInfo } = useAuthStore()

const containerRef = ref<HTMLDivElement>()

const info = ref<TeachingPlanApi.TeachingPlanCreateRecordDetailsOutput>()

async function getPlanInfo() {
  const { error, data } = await fetchTeachingPlanCreateRecordDetails({
    Id: id,
  })
  if (error) {
    throw error
  }
  info.value = data
}

async function getToken() {
  const res: any = await fetch('https://docmee.cn/api/user/createApiToken', {
    method: 'POST',
    body: JSON.stringify({
      uid: userInfo.userId,
      limit: 10,
    }),
    headers: {
      'Api-Key': 'ak_rKykmI535sF65qDY3s',
      'Content-Type': 'application/json',
    },
  })
  const json = await res.json()
  return json.data.token
}

const showSaveBtn = ref(false)

const timer = ref<NodeJS.Timer>()

function createShowSaveTimer() {
  if (timer.value) {
    clearTimeout(timer.value as any as number)
  }
  timer.value = setTimeout(() => {
    // 5000 内没有生成ppt，认为是用户没有操作
    showSaveBtn.value = true
    timer.value = undefined
  }, 5000)
}

const docmeeUIRef = shallowRef()

async function createUI() {
  const token = await getToken()
  docmeeUIRef.value = new DocmeeUI({
    container: containerRef.value!, // 挂载 iframe 容器元素ID
    page: 'creator', // 'dashboard' ppt列表; 'creator' 创建页面; 'customTemplate' 自定义模版; 'editor' 编辑页（需要传pptId字段）
    creatorVersion: 'v2',
    token, // token
    creatorData: {
      content: info.value?.TeachingPlanText,
      creatorNow: true,
      type: CreatorType.MD,
    },
    creatorMode: 'material',
    isMobile: false, // 移动端模式
    padding: '40px 20px 0px',
    background: 'linear-gradient(-157deg,#f57bb0, #867dea)', // 自定义背景
    mode: 'light', // light 亮色模式, dark 暗色模式
    lang: 'zh', // 国际化
    onMessage: (message: any) => {
      if (message.type === 'error') {
        window.$message.error(message.data.message)
      }

      if (timer.value && !showSaveBtn.value) {
        createShowSaveTimer()
      }
      if (message.type === 'beforeCreatePpt') {
        createShowSaveTimer()

        setInterval(() => {
          docmeeUIRef.value?.getCurrentPptInfo?.()
        }, 5000)
      }
    },
  })
}

const router = useRouter()

onMounted(() => {
  if (expires) {
    const isExpired = dayjs(Number(expires)).isBefore(dayjs())
    if (isExpired) {
      window.$message.error('参数错误')
      router.replace({
        name: 'teaching-plan_create',
        query: {
          recordId: id as string,
        },
      })
      return
    }
  }
  getPlanInfo()
    .then(() => {
      createUI()
    })
})

onUnmounted(() => {
  docmeeUIRef.value?.destroy?.()
})
</script>

<template>
  <div class="relative size-full">
    <div ref="containerRef" class="size-full" />

    <div v-if="showSaveBtn" class="absolute bottom-64px right-32px">
      <NButton
        round
        class="!h-42px !w-128px !bg-[linear-gradient(90deg,_#A99FFF_0%,_#CFA3FF_100%)] !text-white"
      >
        保存到教案
      </NButton>
    </div>
  </div>
</template>

<style scoped>

</style>
