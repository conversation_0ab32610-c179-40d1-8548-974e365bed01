<script setup lang="ts">
import MDEditor, { type ActionType } from '@sa/components/common/md-editor/MDEditor.vue'
import BackButton from '@sa/components/common/back-button.vue'
import { getServiceBaseURL } from '@sa/utils'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { NScrollbar } from 'naive-ui'
import { useBoolean, usePromiseDialog, useRequest, useRouterPush } from '@sa/hooks'

import dayjs from 'dayjs'
import LeftView from './components/left-view/index.vue'
import RightView from './components/right-view/index.vue'
import PlanWord from './components/plan-word/index.vue'
import OptimizeModal from './components/optimize-modal/index.vue'
import { useAuthStore } from '@/store/modules/auth'
import {
  fetchGenerateWordTeachingPlan,
  fetchSaveTeachingPlan,
  fetchTeachingPlanCreateRecordDetails,
  fetchTeachingPlanCreateRecordFileUpdateName,
  fetchUpdateTeachingPlanCreateRecordFile,
  fetchUpdateTeachingPlanCreateRecordText,
} from '@/service/api/teaching-plan'

const query = useRoute().query
const recordId = query.recordId as string // 记录ID
const qType = query.type as string // 类型

// 判断是否使用代理
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
// 获取服务基础URL
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)

const formModel = ref<TeachingPlanApi.CreateTeachingPlanInput>({
  Type: 1,
})

const ctrl = shallowRef()

const editorRef = ref<InstanceType<typeof MDEditor>>()

const mdState = ref({
  disabled: false,
  mdContent: '',
})

const docState = ref({
  isCreated: false,
  BusinessId: '', // 生成的教案唯一ID
  isEditing: false,
  docName: '这是教案的标题',
  docTempName: '',
})

const scrollbarRef = ref()

const { userInfo, activeState } = useAuthStore()

function onMarkDownChange() {
  if (!editorRef.value?.isCursorAtEnd()) {
    return
  }
  toBottom()
}

function toBottom() {
  requestAnimationFrame(() => {
    scrollbarRef.value!.scrollTo({ position: 'bottom' })
  })
}

const { data: planWordList, run: fetchPlanWordList } = useRequest(() => {
  return fetchUpdateTeachingPlanCreateRecordFile({
    Id: docState.value.BusinessId,
  })
}, {
  manual: true,
  initialValue: [],
})

async function onUpdateName(item: TeachingPlanApi.TeachingPlanCreateRecordFileOutput) {
  const { error } = await fetchTeachingPlanCreateRecordFileUpdateName({
    Id: item.Id,
    FileName: item.FileName,
  })
  if (error) {
    return
  }
  window.$message?.success('修改成功')
  await fetchPlanWordList()
}

// 创建中loading
const { setTrue: startCreating, setFalse: endCreating, bool: isCreating } = useBoolean(false)

async function fetchRecord() {
  if (!recordId) {
    return
  }
  const { data } = await fetchTeachingPlanCreateRecordDetails({
    Id: recordId,
  })
  if (data) {
    const Id = data.Id
    data.Id = undefined
    formModel.value = {
      ...data,
    }
    docState.value.docName = data.Name!
    docState.value.BusinessId = Id!
    docState.value.isCreated = true
    mdState.value.disabled = false
    mdState.value.mdContent = data.TeachingPlanText ?? ''

    await fetchPlanWordList() // 获取文档列表
  }
}

async function onCreate() {
// 重置AbortController以确保可以正常取消
  ctrl.value = new AbortController()

  startCreating()
  mdState.value.mdContent = ''
  docState.value.BusinessId = '' // 重新创建
  docState.value.isCreated = true

  await nextTick()
  mdState.value.disabled = true

  await fetchEventSource(`${baseURL}/AgentTeacherTeachingPlan/AgentTeacherTeachingPlan/CreateTeachingPlan`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      TeacherId: userInfo.userId,
      SubjectId: activeState.subjectId,
      SchoolId: userInfo.schoolId,
      ...formModel.value,
    }),
    signal: ctrl.value.signal,
    openWhenHidden: true,
    onopen: async (response) => {
      if (!response.ok) {
        endCreating()
        mdState.value.disabled = false
        throw new Error(`HTTP error! status: ${response.status}`)
      }
    },
    onmessage: async (msg) => {
      try {
        const { Content, Success, BusinessId } = JSON.parse(msg.data)
        if (!Success) {
          return
        }

        if (Content === 'DONE') {
          mdState.value.disabled = false
          editorRef.value?.recreate() // 强制刷新
          // 教案ID
          docState.value.BusinessId = BusinessId
          endCreating()

          nextTick(() => {
            toBottom()
          })
          return
        }

        mdState.value.mdContent += (Content ?? '')
        requestAnimationFrame(() => {
          toBottom()
        })
      }
      catch {
      }
    },
    onclose: () => {
      endCreating()
      mdState.value.disabled = false
    },
    onerror: (_err) => {
      endCreating()
      mdState.value.disabled = false
      throw _err
    },
  })
}

// 保存教案
async function onSavePlan() {
  const { error } = await fetchSaveTeachingPlan({
    Type: 1,
    Name: docState.value.docName,
    Text: mdState.value.mdContent,
  })
  if (error) {
    return
  }
  window.$message?.success('教案保存成功')
}

async function onSaveMarkDown() {
  const { error } = await fetchUpdateTeachingPlanCreateRecordText({
    Id: docState.value.BusinessId,
    TeachingPlanText: mdState.value.mdContent,
  })
  if (error) {
    throw error
  }
}

// 生成word教案文档
async function onGenerateWordPlan() {
  await onSaveMarkDown() // 先保存markdown
  const { error } = await fetchGenerateWordTeachingPlan({
    Id: docState.value.BusinessId,
  })
  if (error) {
    return
  }
  fetchPlanWordList()
  window.$message?.success('生成成功')
}

const { routerPushByKey } = useRouterPush()

const shouldAcceptPush = ref(false)

async function startGenPPT() {
  await onSaveMarkDown() // 先保存markdown
  shouldAcceptPush.value = true
  routerPushByKey('teaching-plan_ppt-gen', {
    query: {
      id: docState.value.BusinessId as string,
      // 过期时间
      expires: `${dayjs().add(10, 'second').valueOf()}`,
    },
  })
}

const { showPromiseDialog } = usePromiseDialog()

async function onChangeType(type: number) {
  if (docState.value.BusinessId) {
    // 已创建，不允许切换类型
    try {
      await showPromiseDialog({
        title: '提示',
        content: '切换后，当前已生成的教案内容将会清空（可在最近生成记录查看），是否继续？',
        positiveText: '继续',
        negativeText: '取消',
      })
    }
    catch {
      return
    }
  }
  docState.value.isCreated = false
  docState.value.BusinessId = ''
  mdState.value.mdContent = ''
  formModel.value.Type = type
}

onMounted(() => {
  if (qType) {
    formModel.value.Type = Number(qType)
  }
  fetchRecord()
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

function handleBeforeUnload(e: BeforeUnloadEvent) {
  e.preventDefault()
  e.returnValue = '' // 触发浏览器的提示框
}

onBeforeRouteLeave((_to, _from, next) => {
  if (shouldAcceptPush.value) {
    next()
    return
  }
  showPromiseDialog({
    title: '提示',
    content: '你有未保存的更改，确定离开吗？',
    positiveText: '确定',
    negativeText: '取消',
  })
    .then(() => {
      next() // 允许跳转
    })
    .catch(() => {
      next(false) // 阻止跳转
    })
})

const planState = ref({
  show: false,
})

const optimizeModal = ref({
  show: false,
  action: '',
  optimizeText: '',
  recordId: '',
  range: {
    from: 0,
    to: 0,
  },
})

function onEditorAction(action: 'prefect' | 'expand' | 'reduce', options: { text: string, coords: ActionType }) {
  optimizeModal.value.action = (
    ({
      prefect: '润色',
      expand: '扩写',
      reduce: '精简',
    })[action]
  )
  const selection = editorRef.value?.getSelection()
  if (!selection) {
    return
  }
  optimizeModal.value.range = {
    from: selection.from,
    to: selection.to,
  }
  optimizeModal.value.optimizeText = options.text
  optimizeModal.value.recordId = docState.value.BusinessId
  optimizeModal.value.show = true
}
</script>

<template>
  <div class="relative h-full w-full flex py-16px pt-64px">
    <BackButton />

    <!-- 背景 -->
    <div class="fixed inset-0 z-0 h-full">
      <div class="absolute inset-0 h-full w-full bg-[#F2F6FC]" />
      <div
        class="absolute inset-0 h-full w-full from-[#8AD1F9] via-[#F3F6FF] to-[#ECD9FF] bg-gradient-to-b opacity-80"
      />
    </div>

    <div class="h-full w-full flex">
      <NScrollbar class="mr-16px h-full w-fit rounded-8px bg-white">
        <div class="h-full w-fit bg-white p-16px">
          <LeftView
            v-model="formModel"
            :business-id="docState.BusinessId"
            @submit="onCreate"
            @change-type="onChangeType"
          />
        </div>
      </NScrollbar>
      <div class="relative h-full min-w-0 flex-1 border-2 border-white rounded-16px p-16px">
        <div v-if="docState.isCreated" class="size-full flex flex-col overflow-hidden rounded-16px bg-white p-8px">
          <div
            class="w-full flex shrink-0 items-center py-8px"
            :class="[
              docState.BusinessId ? 'opacity-100' : 'opacity-0',
            ]"
          >
            <div class="min-h-40px flex flex-1 items-center">
              <template v-if="planWordList?.[0]">
                <img class="size-24px" src="@/assets/imgs/teaching-plan/file-word.png" alt="">
                <span class="whitespace-nowrap font-bold">教案名称：</span>
                <template v-if="!docState.isEditing">
                  <span class="line-clamp-1 text-[#6E6F70]">{{ planWordList?.[0].FileName }}</span>
                  <img
                    class="ml-12px size-20px cursor-pointer"
                    src="@/assets/imgs/teaching-plan/ic_edit.png"
                    alt=""
                    @click="() => {
                      docState.isEditing = !docState.isEditing
                      docState.docTempName = planWordList?.[0].FileName!
                    }"
                  >
                </template>
                <template v-else>
                  <NInput v-model:value="docState.docTempName" class="text-[#6E6F70] !w-200px" />
                  <div>
                    <NButton
                      quaternary
                      type="info"
                      @click="() => {
                        docState.isEditing = !docState.isEditing
                        planWordList![0].FileName = docState.docTempName
                        onUpdateName(planWordList![0])
                      }"
                    >
                      确定
                    </NButton>
                    <NButton
                      quaternary
                      type="info"
                      @click="docState.isEditing = !docState.isEditing"
                    >
                      取消
                    </NButton>
                  </div>
                </template>
              </template>
            </div>
            <div>
              <NButton quaternary type="info" @click="planState.show = true">
                文件下载
              </NButton>
            </div>
          </div>
          <div class="min-h-0 flex-1">
            <NScrollbar ref="scrollbarRef" class="h-full border-1 border-gray-200 rounded-8px bg-white">
              <MDEditor
                ref="editorRef"
                v-model:value="mdState.mdContent"
                :disabled="mdState.disabled"
                @update:value="onMarkDownChange"
                @on-action="onEditorAction"
              />
            </NScrollbar>
          </div>
          <div
            class="flex shrink-0 justify-end gap-x-16px py-16px"
            :class="[
              docState.BusinessId ? 'opacity-100' : 'opacity-0',
            ]"
          >
            <NButton
              size="large"
              strong
              round
              ghost
              type="primary"
              @click="onSavePlan"
            >
              添加到教案列表
            </NButton>
            <NButton size="large" strong round ghost type="primary" @click="onGenerateWordPlan">
              生成word教案文档
            </NButton>
            <NButton
              size="large"
              class="!text-white"
              round
              style="background: linear-gradient(90deg, #A99FFF 0%, #CFA3FF 100%)"
              @click="startGenPPT"
            >
              一键生成PPT
            </NButton>
          </div>
        </div>

        <div v-else class="h-full">
          <RightView />
        </div>
      </div>
    </div>

    <!--    生成文档中的loading框 -->
    <NModal :show="isCreating" :close-on-es="false" :mask-closable="false" :style="{ '--n-box-shadow': 'none' }">
      <div class="flex flex-col items-center gap-y-16px text-white">
        <img class="h-84px w-89px" src="@/assets/imgs/teaching-plan/ai-logo.png" alt="">
        <span class="text-22px font-bold">正在生成教案中，请稍等...</span>
        <NSpin :show="true" />
      </div>
    </NModal>

    <PlanWord
      v-if="planWordList"
      v-model:show="planState.show"
      v-model:plan-word-list="planWordList"
      :business-id="docState.BusinessId"
      @on-update-name="onUpdateName"
    />

    <OptimizeModal
      v-model:show="optimizeModal.show"
      :record-id="optimizeModal.recordId"
      :optimize-text="optimizeModal.optimizeText"
      :action="optimizeModal.action"
      @replace="e => {
        editorRef?.replaceRange(e, optimizeModal.range)
      }"
      @insert="e => {
        editorRef?.insert(e, false)
      }"
    />
  </div>
</template>

<style scoped>

</style>
