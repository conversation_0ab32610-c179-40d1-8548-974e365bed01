/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AnimationCard: typeof import('./../../../../packages/base/components/common/chat/AnimationCard.vue')['default']
    AppProvider: typeof import('./../../../../packages/base/components/common/app-provider.vue')['default']
    AudioMessage: typeof import('./../../../../packages/base/components/common/chat/message-components/AudioMessage.vue')['default']
    BackButton: typeof import('./../../../../packages/base/components/common/back-button.vue')['default']
    BetterScroll: typeof import('./../../../../packages/base/components/custom/better-scroll.vue')['default']
    BubbleList: typeof import('./../../../../packages/base/components/common/chat/BubbleList.vue')['default']
    ButtonIcon: typeof import('./../../../../packages/base/components/custom/button-icon.vue')['default']
    ChatContainer: typeof import('./../../../../packages/base/components/common/chat/ChatContainer.vue')['default']
    ChatSender: typeof import('./../../../../packages/base/components/common/chat/ChatSender.vue')['default']
    CkEditor: typeof import('./../../../../packages/base/components/common/ck-editor/index.vue')['default']
    CodeCard: typeof import('./../../../../packages/base/components/common/code/CodeCard.vue')['default']
    CountTo: typeof import('./../../../../packages/base/components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./../../../../packages/base/components/common/dark-mode-container.vue')['default']
    ExceptionBase: typeof import('./../../../../packages/base/components/common/exception-base.vue')['default']
    FileMessage: typeof import('./../../../../packages/base/components/common/chat/message-components/FileMessage.vue')['default']
    FilesSelect: typeof import('./../../../../packages/base/components/common/chat/FilesSelect.vue')['default']
    FileUpload: typeof import('./../../../../packages/base/components/common/file-upload.vue')['default']
    FullScreen: typeof import('./../../../../packages/base/components/common/full-screen.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    IconLocalCodePlus: typeof import('~icons/local/code-plus')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconLocalTitle: typeof import('~icons/local/title')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    IconUilSearch: typeof import('~icons/uil/search')['default']
    ImageMessage: typeof import('./../../../../packages/base/components/common/chat/message-components/ImageMessage.vue')['default']
    List: typeof import('./../../../../packages/base/components/common/list/List.vue')['default']
    LookForward: typeof import('./../../../../packages/base/components/custom/look-forward.vue')['default']
    MDEditor: typeof import('./../../../../packages/base/components/common/md-editor/MDEditor.vue')['default']
    MenuToggler: typeof import('./../../../../packages/base/components/common/menu-toggler.vue')['default']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBackTop: typeof import('naive-ui')['NBackTop']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NPopover: typeof import('naive-ui')['NPopover']
    NQrCode: typeof import('naive-ui')['NQrCode']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../../../../packages/base/components/common/pin-toggler.vue')['default']
    PreviewCode: typeof import('./../../../../packages/base/components/common/code/PreviewCode.vue')['default']
    QsProvide: typeof import('./../../../../packages/base/components/common/questions/qs-provide/index.vue')['default']
    QuestionItemContainer: typeof import('./../../../../packages/base/components/common/questions/question-item-container.vue')['default']
    QuestionListContainer: typeof import('./../../../../packages/base/components/common/questions/question-list-container.vue')['default']
    ReloadButton: typeof import('./../../../../packages/base/components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SoybeanAvatar: typeof import('./../../../../packages/base/components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../../../../packages/base/components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../../../../packages/base/components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../../../../packages/base/components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../../../../packages/base/components/advanced/table-header-operation.vue')['default']
    ThemeSchemaSwitch: typeof import('./../../../../packages/base/components/common/theme-schema-switch.vue')['default']
    VoicePlayButton: typeof import('./../../../../packages/base/components/common/chat/VoicePlayButton.vue')['default']
    WaveBg: typeof import('./../../../../packages/base/components/custom/wave-bg.vue')['default']
  }
}
