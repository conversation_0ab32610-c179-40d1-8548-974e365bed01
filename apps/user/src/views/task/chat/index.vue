<script setup lang="ts">
import ChatContainer from '@sa/components/common/chat/ChatContainer.vue'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { convertHistoryDataToMessages, getServiceBaseURL } from '@sa/utils'
import { useRouterPush } from '@sa/hooks'
import { XMarkdown } from 'vue-element-plus-x'
import CustomDrawerContent from './components/custom-drawer-content.vue'
import {
  CHAT_CONFIG as CONFIG,
  createAIMessage,
  createSystemMessage as createSystemMsg,
  formatApiParams,
} from './utils/chat-helpers'
import { fetchAgentStudentOralCommunicationSubmit, fetchGetAIDialogueContentRecord, fetchGetOralCommunicationDetail, fetchGetOralCommunicationStudentResult } from '@/service/api'
import { useAuthStore } from '@/store/modules/auth'

defineOptions({
  name: 'TaskChat',
})

const { routerBack } = useRouterPush()
const route = useRoute()
const authStore = useAuthStore()
const { userInfo } = authStore
const AgentBotCode = 'Agent_OralCommunication'
// 环境配置
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)
const aiName = import.meta.env.VITE_AI_NAME
const aiAvatar = import.meta.env.VITE_AI_AVAYAR

// 路由参数
const queryParams = computed(() => ({
  AgentId: route.query.AgentId as string,
  AgentTaskId: route.query.AgentTaskId as string,
  StudentDoTaskState: route.query.StudentDoTaskState as 'SUBMIT' | 'DISPLAY',
  AgentBotCode: route.query.AgentBotCode as string,
}))
const isShowAudio = computed(() => queryParams.value.AgentBotCode === AgentBotCode)
// API调用参数（不包含StudentDoTaskState）
const apiParams = computed(() => ({
  AgentId: route.query.AgentId as string,
  AgentTaskId: route.query.AgentTaskId as string,
}))

// 响应式状态
const ctrl = ref(new AbortController())
const isLoading = ref(false)
const isSubmitting = ref(false)
const drawerVisible = ref(true)
const chatContainerRef = ref<InstanceType<typeof ChatContainer>>()
const initialMessages = ref<Chat.MessageItem[]>()
const studentResultInfo = reactive({
  AssessmentResult: '',
})

// 用户信息（计算属性，确保响应式）
const chatUserInfo = computed(() => ({
  userName: userInfo.userName,
  userAvatar: userInfo.Photo,
}))

// 智能体口语交际详情状态
const oralCommunicationInfo = ref<{
  logo: string
  oralCommunicationDetails: Task.AgentModelAISaveTeacherOralCommunicationInput
}>({
  logo: '',
  oralCommunicationDetails: {
    Prologue: '',
    TimeRange: [],
    Name: '',
    InteractiveMode: 0,
    Introduce: '',
  },
})

// 分页状态管理 (0表示没有更多数据)
const historyPageIndex = ref<number>(CONFIG.PAGINATION.INITIAL_PAGE)

/**
 * 初始化数据
 * 并行加载历史消息和口语交际详情
 */
async function initData() {
  await Promise.all([
    fetchHistoryMessages(),
    getOralCommunicationDetail(),
  ])
}
async function requestStudentResult() {
  const res = await fetchGetOralCommunicationStudentResult({
    AgentTaskId: apiParams.value.AgentTaskId,
    StudentId: userInfo.userId,
  })
  if (!res.error) {
    studentResultInfo.AssessmentResult = res.data.AssessmentResult
  }
}
/**
 * 公共的历史消息请求逻辑
 * @returns 历史消息数组或null
 */
async function requestHistoryMessages(): Promise<Chat.MessageItem[] | null> {
  try {
    const { data, error } = await fetchGetAIDialogueContentRecord(
      formatApiParams(apiParams.value, historyPageIndex.value, userInfo),
    )

    if (error) {
      console.error('获取历史消息失败:', error)
      return null
    }

    if (data?.TotalCount && data.Datas && data.Datas.length > 0) {
      // 转换历史消息数据为组件所需格式
      const historyMessages = convertHistoryDataToMessages({
        historyData: data.Datas,
        userInfo: chatUserInfo.value,
        aiInfo: { aiName, aiAvatar },
      })

      // 增加页码，准备下次加载
      historyPageIndex.value++
      return historyMessages
    }
    else {
      // 没有更多数据时，将 PageIndex 设为 0，表示不再加载
      historyPageIndex.value = 0
      return []
    }
  }
  catch (error) {
    console.error('请求历史消息异常:', error)
    return null
  }
}

// 初始化时获取历史对话记录
async function fetchHistoryMessages() {
  const historyMessages = await requestHistoryMessages()
  if (historyMessages) {
    initialMessages.value = historyMessages
  }
}

/**
 * 获取口语交际任务详情并设置初始消息
 * 1. 根据任务ID获取口语交际详情
 * 2. 如果获取成功，使用开场白作为AI的初始消息
 * 3. 如果获取失败，则不做任何处理
 */
async function getOralCommunicationDetail() {
  try {
    const { data, error } = await fetchGetOralCommunicationDetail({
      Id: apiParams.value.AgentTaskId,
    })

    if (error) {
      console.error('获取口语交际详情失败:', error)
      return
    }

    // 更新口语交际信息
    oralCommunicationInfo.value = {
      logo: data.Logo,
      oralCommunicationDetails: data.OralCommunicationInfo,
    }

    // 只有在没有历史消息时才设置开场白作为初始消息
    if (!initialMessages.value?.length && data.OralCommunicationInfo.Prologue) {
      initialMessages.value = [createSystemMsg(data.OralCommunicationInfo.Prologue, aiName, aiAvatar)]
    }
  }
  catch (error) {
    console.error('获取口语交际详情异常:', error)
  }
}

/**
 * 处理开始 SSE 事件
 * @param data 用户输入数据
 * @param data.inputContent 输入的文本内容
 * @param data.uploadedFiles 上传的文件列表
 * @param data.audioData 音频数据
 */
async function handleStartSSE(data: {
  inputContent: string
  uploadedFiles: any[]
  audioData?: any
}) {
  const { inputContent, audioData } = data

  const serverData: Task.AgentStudentOralCommunicationDialogueInputRequest = {
    AgentId: apiParams.value.AgentId,
    AgentTaskId: apiParams.value.AgentTaskId,
    ClassId: userInfo.ClassId,
    StudentId: userInfo.userId,
    Msg: inputContent,
    AudioUrl: audioData?.audioUrl,
    Duration: audioData?.duration,
  }

  isLoading.value = true
  fetchAgentStudentOralCommunicationDialogue(serverData)
}
/**
 * 发起智能体口语交际对话的SSE请求
 * @param data 对话请求数据
 */
function fetchAgentStudentOralCommunicationDialogue(data: Task.AgentStudentOralCommunicationDialogueInputRequest) {
  // 创建AI回复消息
  const systemMessage = reactive(createAIMessage(aiName, aiAvatar))

  // 确保消息列表存在并添加AI消息
  if (!initialMessages.value) {
    initialMessages.value = []
  }
  initialMessages.value.push(systemMessage)

  // 重置AbortController以确保可以正常取消
  ctrl.value = new AbortController()

  fetchEventSource(`${baseURL}/AgentStudentOralCommunication/AgentStudentOralCommunication/AgentStudentOralCommunicationDialogue`, {
    method: 'POST',
    headers: {
      'Content-Type': CONFIG.SSE.CONTENT_TYPE,
    },
    body: JSON.stringify(data),
    signal: ctrl.value.signal,
    openWhenHidden: true,
    onopen: async (response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
    },
    onmessage: async (msg) => {
      try {
        const { Content, Success } = JSON.parse(msg.data)
        systemMessage.loading = false

        if (!Success) {
          handleSSEError(Content)
          return
        }

        if (!Content.startsWith(CONFIG.SSE.DONE_PREFIX)) {
          systemMessage.content += Content
        }
        else {
          handleSSEComplete()
        }
      }
      catch (error) {
        console.error('解析SSE消息失败:', error)
        handleSSEError('消息解析失败')
      }
    },
    onclose: () => {
      isLoading.value = false
    },
    onerror: (err) => {
      throw err
      console.error('SSE连接错误:', err)
      handleSSEError('连接失败，请重试')
    },
  })
}

/**
 * 处理SSE错误
 * @param message 错误消息
 */
function handleSSEError(message: string) {
  ctrl.value.abort()
  isLoading.value = false
  window.$message?.error(message)
}

/**
 * 处理SSE完成
 */
function handleSSEComplete() {
  ctrl.value.abort()
  isLoading.value = false
}
/**
 * 处理取消 SSE 事件
 */
function handleCancelSSE() {
  isLoading.value = false
  ctrl.value.abort()
}

/**
 * 提交任务
 */
async function handleSubmitTask() {
  isSubmitting.value = true
  try {
    const res = await fetchAgentStudentOralCommunicationSubmit({
      ClassId: userInfo.ClassId,
      ...apiParams.value,
      StudentId: userInfo.userId,
    })

    if (res.error) {
      window.$message?.error('提交失败')
      return
    }

    window.$message?.success('提交成功')
    routerBack()
  }
  catch (error) {
    console.error('提交任务失败:', error)
    window.$message?.error('提交失败')
  }
  finally {
    isSubmitting.value = false
  }
}
/**
 * 加载更多历史消息
 * 实现上拉加载更多功能，将新消息插入到列表顶部
 * 并保持用户当前的阅读位置
 */
async function loadMoreMessages() {
  // 如果没有更多数据可加载，直接返回
  if (!historyPageIndex.value) {
    chatContainerRef.value?.finishLoadMore()
    return
  }

  try {
    const historyMessages = await requestHistoryMessages()

    if (historyMessages && historyMessages.length > 0) {
      // 将新的历史消息插入到现有消息列表的前面
      if (initialMessages.value) {
        initialMessages.value = [...historyMessages, ...initialMessages.value]
      }
      else {
        initialMessages.value = historyMessages
      }

      // 等待DOM更新后，滚动到加载前的第一条消息位置
      await nextTick()
      // 原来的第一条消息现在的索引是新加载的消息数量
      const targetIndex = historyMessages.length
      chatContainerRef.value?.scrollToBubble(targetIndex)
    }
  }
  catch (error) {
    console.error('加载历史消息异常:', error)
  }
  finally {
    // 无论成功还是失败，都要重置加载状态
    chatContainerRef.value?.finishLoadMore()
  }
}
// 计算属性
const isShowViewDrawer = computed(() => {
  return queryParams.value.StudentDoTaskState === 'SUBMIT'
})

const drawerTransitionStyle = computed(() => ({
  transitionDuration: CONFIG.DRAWER.TRANSITION_DURATION,
}))

const markdownCodeSlot = {
  codeHeaderControl: () => h('div', ''),
}
/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  initData()
  // 只有当StudentDoTaskState为DISPLAY时才调用requestStudentResult接口
  if (queryParams.value.StudentDoTaskState === 'DISPLAY') {
    requestStudentResult()
  }
})

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  // 取消正在进行的SSE连接
  if (ctrl.value) {
    ctrl.value.abort()
  }
})
</script>

<template>
  <div class="relative h-full flex justify-between gap-12px">
    <!-- 主内容区域 -->
    <div
      class="h-full transition-all ease-in-out"
      :style="{ transitionDuration: CONFIG.DRAWER.TRANSITION_DURATION, width: drawerVisible ? `calc(100% - ${CONFIG.DRAWER.WIDTH})` : '100%' }"
    >
      <!-- 顶部按钮区域 -->
      <ButtonIcon
        class="position-absolute right-20px top-20px z-10"
        :icon="drawerVisible ? 'mdi:close' : 'mdi:menu'"
        :tooltip-content="drawerVisible ? '关闭侧边栏' : '打开侧边栏'"
        @click="drawerVisible = !drawerVisible"
      />

      <!-- 聊天容器 -->
      <ChatContainer
        ref="chatContainerRef"
        v-model:initial-messages="initialMessages"
        :is-loading="isLoading"
        :chat-user-info="chatUserInfo"
        :show-sender="isShowViewDrawer"
        :show-audio="isShowAudio"
        @start-sse="handleStartSSE"
        @cancel-sse="handleCancelSSE"
        @load-more="loadMoreMessages"
      />
    </div>

    <!-- 右侧抽屉区域 -->
    <div
      class="h-full overflow-hidden border-l border-gray-200 bg-white transition-all ease-in-out"
      :class="drawerVisible ? 'opacity-100' : 'w-0! opacity-0'"
      :style="{
        ...drawerTransitionStyle,
        width: drawerVisible ? CONFIG.DRAWER.WIDTH : '0',
      }"
    >
      <!-- 任务提交区域 -->
      <template v-if="isShowViewDrawer">
        <NScrollbar class="h-full w-full">
          <CustomDrawerContent
            :oral-communication-info="oralCommunicationInfo"
            :is-submitting="isSubmitting"
            @submit-task="handleSubmitTask"
          />
        </NScrollbar>
      </template>

      <!-- AI评估结果区域 -->
      <template v-else>
        <div class="h-full w-full flex flex-col">
          <!-- 评估结果标题栏 -->
          <div class="[bg-linear-gradient(var(--un-gradient))] h-70px w-full flex shrink-0 items-center justify-between from-[rgba(54,192,253,0.1)] via-[rgba(54,120,253,0.1)] to-[rgba(154,54,253,0.1)] pl-12px pr-40px">
            <span class="from-[#36BCFD] via-[#7193FF] to-[#E572FE] bg-gradient-to-r bg-clip-text text-24px text-transparent font-500">
              AI评估结果
            </span>
            <span class="text-14px text-[#7E51E9]">
              此内容由AI生成，仅供参考
            </span>
          </div>

          <!-- 评估结果内容区域 -->
          <div class="min-h-0 w-full flex-1">
            <NScrollbar class="h-full w-full">
              <XMarkdown
                v-if="studentResultInfo?.AssessmentResult"
                :markdown="studentResultInfo.AssessmentResult"
                :code-x-slot="markdownCodeSlot"
                default-theme-mode="light"
                class="w-full p-16px"
              />

              <NEmpty v-else description="暂无评估结果" class="pos-absolute h-full w-full flex-center text-gray-400" />
            </NScrollbar>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
