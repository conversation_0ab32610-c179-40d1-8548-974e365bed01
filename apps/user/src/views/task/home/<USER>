<script setup lang="ts">
import { usePaginatedRequest, useRouterPush } from '@sa/hooks'
import List from '@sa/components/common/list/List.vue'
import TaskCard from './components/task-card.vue'
import { fetchGetStudentHomePageAgentTaskList } from '@/service/api'
import { useAuthStore } from '@/store/modules/auth'

defineOptions({
  name: 'TaskManagement',
})

const authStore = useAuthStore()
const { routerPushByKey } = useRouterPush()
const { userInfo, subjectState } = authStore

// 标签页选项配置
const tabOptions = reactive([
  { key: 1, label: '进行中', count: 0 },
  { key: 2, label: '已完成', count: 0 },
])

// 查询参数
const queryParams = reactive({
  AgentTaskState: 1,
  PageIndex: 1,
  PageSize: 10,
})

// 查询响应数据
const queryResponse = reactive<{
  taskData: Task.AgentStudentHomePageAgentTaskOuput[]
  TotalCount: number
}>({
  taskData: [],
  TotalCount: 0,
})
const { data: list, run: initTaskData, pagination, loadMore, loading, loadingMore } = usePaginatedRequest(({
  current,
}) => {
  return fetchGetStudentHomePageAgentTaskList({
    PageIndex: current,
    PageSize: 20,
    AgentTaskState: queryParams.AgentTaskState,
    ClassId: userInfo.ClassId,
    SchoolId: userInfo.SchoolId,
    StudentId: userInfo.userId,
    SubjectId: subjectState.currentSubjectId,
  })
}, {
  transform: (data) => {
    queryResponse.taskData = data?.Datas || []
    tabOptions.filter(v => v.key === queryParams.AgentTaskState)[0].count = data?.TotalCount || 0
    queryResponse.TotalCount = data?.TotalCount || 0

    return {
      list: data.Datas || [],
      total: data.TotalCount,
    }
  },
  manual: true,
})
// 处理标签页切换
function handleTabChange(key: number) {
  queryParams.AgentTaskState = key
  initTaskData()
}

// 处理按钮点击
function handleButtonClick(task: Task.AgentStudentHomePageAgentTaskOuput) {
  const { AgentId, AgentTaskId, AgentTaskState, StudentDoTaskState, AgentBotCode } = task
  routerPushByKey('task_chat', {
    query: {
      AgentId,
      AgentTaskId,
      StudentDoTaskState: AgentTaskState === 1 && StudentDoTaskState === 2 ? 'SUBMIT' : 'DISPLAY',
      AgentBotCode,
    },
  })
}
// 初始化加载
onMounted(async () => {
  initTaskData()
})
</script>

<template>
  <div class="h-full flex-col-stretch gap-16px">
    <!-- 标签页导航 -->
    <NTabs
      :value="queryParams.AgentTaskState"
      type="line"
      size="large"
      class="task-tabs pl-20px"
      @update:value="handleTabChange"
    >
      <NTab
        v-for="tab in tabOptions"
        :key="tab.key"
        :name="tab.key"
        class="tab-item"
      >
        <div class="flex items-center gap-8px">
          <span class="text-16px">{{ tab.label }}</span>
          <span
            class="min-w-20px rounded-full px-8px py-2px text-center text-12px transition-all duration-300"
            :class="[
              queryParams.AgentTaskState === tab.key
                ? 'bg-primary text-white shadow-md'
                : 'bg-gray-100 text-gray-600',
            ]"
          >
            {{ tab.count }}
          </span>
        </div>
      </NTab>
    </NTabs>

    <div class="min-h-0 flex-1">
      <!-- 任务卡片列表 -->
      <List
        :pagination="pagination"
        :loading="loading"
        :loading-more="loadingMore"
        @load-more="loadMore"
      >
        <!-- 任务卡片列表 -->
        <NGrid v-if="list.length > 0" responsive="screen" item-responsive :cols="24" :x-gap="16" :y-gap="16">
          <NGridItem
            v-for="task in list"
            :key="task.AgentId"
            span="24 s:24 m:12 l:12 xl:12"
          >
            <TaskCard
              :task="task"
              @button-click="handleButtonClick"
            />
          </NGridItem>
        </NGrid>
        <NEmpty v-else class="flex flex-col items-center justify-center py-80px text-gray-500" :description="`${queryParams.AgentTaskState === 1 ? '完美，您当前没有进行中的任务' : '您还没有完成任何任务，继续努力吧！'}`" />
      </List>
    </div>
  </div>
</template>
