/**
 * 华为云 OBS 上传工具类
 * 基于华为云 OBS 浏览器端 SDK (esdk-obs-browserjs) 实现
 */
import ObsClient from 'esdk-obs-browserjs'

const accessKeyId = import.meta.env.VITE_OBS_ACCESS_KEY_ID
const accessKeySecret = import.meta.env.VITE_OBS_SECRET_ACCESS_KEY

export interface OBSConfig {
  /** 访问密钥 ID */
  accessKeyId?: string
  /** 访问密钥 Secret */
  accessKeySecret?: string
  /** 安全令牌（临时凭证时需要） */
  securityToken?: string
  /** 区域 */
  region?: string
  /** 存储桶名称 */
  bucket: string
  /** 服务端点（可选，默认使用华为云标准端点） */
  endpoint?: string
  /** 自定义域名 */
  customDomain?: string
}

export interface UploadOptions {
  /** 文件对象 */
  file: File
  /** 文件路径（可选，不传则自动生成） */
  key?: string
  /** 上传进度回调 */
  onProgress?: (progress: number) => void
  /** 自定义元数据 */
  metadata?: Record<string, string>
  /** ACL 权限 */
  acl?: 'private' | 'public-read' | 'public-read-write'
  /** 存储类别 */
  storageClass?: 'STANDARD' | 'WARM' | 'COLD'
}

export interface UploadResult {
  /** 文件访问 URL */
  url: string
  /** 文件在存储桶中的路径 */
  key: string
  /** 文件大小 */
  size: number
  /** 上传时间 */
  uploadTime: Date
  /** ETag */
  etag?: string
  /** 版本ID */
  versionId?: string
}

export interface SetMetadataOptions {
  /** 对象键名 */
  key: string
  /** Content-Disposition 头部值 */
  contentDisposition: string
  /** 自定义元数据 */
  MetadataDirective?: string
}

/**
 * 华为云 OBS 上传器
 */
export class HuaweiOBSUploader {
  private config: OBSConfig
  private readonly obsClient: ObsClient

  constructor(
    config: Omit<OBSConfig, 'bucket'> & {
      bucket?: string
    } = {},
  ) {
    this.config = {
      bucket: 'aidialoguefileu',
      accessKeyId,
      accessKeySecret,
      region: 'cn-east-2',
      ...(config ?? {}),
    }

    // 初始化 OBS 客户端
    this.obsClient = new ObsClient({
      access_key_id: this.config.accessKeyId!,
      secret_access_key: this.config.accessKeySecret!,
      server:
        this.config.endpoint
        || `https://obs.${this.config.region}.myhuaweicloud.com`,
    })
  }

  /**
   * 上传文件到华为云 OBS
   */
  async upload(options: UploadOptions): Promise<UploadResult> {
    const { file, key, onProgress } = options

    // 生成文件路径
    const fileKey = key || this.generateFileName(file.name)

    try {
      // 准备上传参数
      const uploadParams = {
        Bucket: this.config.bucket,
        Key: fileKey,
        SourceFile: file,
        // 进度回调
        ProgressCallback: onProgress
          ? (transferredAmount: number, totalAmount: number) => {
              const progress = Math.round(
                (transferredAmount / totalAmount) * 100,
              )
              onProgress(progress)
            }
          : undefined,
      }

      // 执行上传
      const result = await new Promise<any>((resolve, reject) => {
        this.obsClient.putObject(uploadParams, (err: any, data: any) => {
          if (err) {
            console.error('OBS 上传错误:', err)
            reject(
              new Error(`上传失败: ${err.message || err.code || '未知错误'}`),
            )
          }
          else {
            resolve(data)
          }
        })
      })

      // 构建访问 URL
      const accessUrl = this.buildAccessUrl(fileKey)

      return {
        url: accessUrl,
        key: fileKey,
        size: file.size,
        uploadTime: new Date(),
        etag: result.ETag,
        versionId: result.VersionId,
      }
    }
    catch (error) {
      console.error('华为云 OBS 上传失败:', error)
      throw error instanceof Error ? error : new Error('上传失败')
    }
  }

  /**
   * 分片上传（适用于大文件）
   */
  async uploadLargeFile(options: UploadOptions): Promise<UploadResult> {
    const {
      file,
      key,
      onProgress,
      metadata = {},
      acl = 'public-read',
      storageClass = 'STANDARD',
    } = options

    const fileKey = key || this.generateFileName(file.name)
    const fileMimeType = this.getMimeType(file.name)

    try {
      // 初始化分片上传
      const initResult = await new Promise<any>((resolve, reject) => {
        this.obsClient.initiateMultipartUpload(
          {
            Bucket: this.config.bucket,
            Key: fileKey,
            ContentType: fileMimeType,
            ACL: acl,
            StorageClass: storageClass,
            Metadata: metadata,
          },
          (err: any, data: any) => {
            if (err)
              reject(err)
            else resolve(data)
          },
        )
      })

      const uploadId = initResult.UploadId
      const chunkSize = 5 * 1024 * 1024 // 5MB 每片
      const chunks = Math.ceil(file.size / chunkSize)
      const parts: any[] = []

      // 上传分片
      for (let i = 0; i < chunks; i++) {
        const start = i * chunkSize
        const end = Math.min(start + chunkSize, file.size)
        const chunk = file.slice(start, end)

        const partResult = await new Promise<any>((resolve, reject) => {
          this.obsClient.uploadPart(
            {
              Bucket: this.config.bucket,
              Key: fileKey,
              PartNumber: i + 1,
              UploadId: uploadId,
              Body: chunk,
            },
            (err: any, data: any) => {
              if (err)
                reject(err)
              else resolve(data)
            },
          )
        })

        parts.push({
          PartNumber: i + 1,
          ETag: partResult.ETag,
        })

        // 更新进度
        if (onProgress) {
          const progress = Math.round(((i + 1) / chunks) * 100)
          onProgress(progress)
        }
      }

      // 完成分片上传
      const completeResult = await new Promise<any>((resolve, reject) => {
        this.obsClient.completeMultipartUpload(
          {
            Bucket: this.config.bucket,
            Key: fileKey,
            UploadId: uploadId,
            Parts: parts,
          },
          (err: any, data: any) => {
            if (err)
              reject(err)
            else resolve(data)
          },
        )
      })

      const accessUrl = this.buildAccessUrl(fileKey)

      return {
        url: accessUrl,
        key: fileKey,
        size: file.size,
        uploadTime: new Date(),
        etag: completeResult.ETag,
        versionId: completeResult.VersionId,
      }
    }
    catch (error) {
      console.error('华为云 OBS 分片上传失败:', error)
      throw error instanceof Error ? error : new Error('分片上传失败')
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(key: string): Promise<void> {
    try {
      await new Promise<void>((resolve, reject) => {
        this.obsClient.deleteObject(
          {
            Bucket: this.config.bucket,
            Key: key,
          },
          (err: any) => {
            if (err)
              reject(err)
            else resolve()
          },
        )
      })
    }
    catch (error) {
      console.error('删除文件失败:', error)
      throw error instanceof Error ? error : new Error('删除文件失败')
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(key: string): Promise<any> {
    try {
      const result = await new Promise<any>((resolve, reject) => {
        this.obsClient.getObjectMetadata(
          {
            Bucket: this.config.bucket,
            Key: key,
          },
          (err: any, data: any) => {
            if (err)
              reject(err)
            else resolve(data)
          },
        )
      })

      return {
        contentLength: result.ContentLength,
        contentType: result.ContentType,
        lastModified: result.LastModified,
        etag: result.ETag,
        metadata: result.Metadata,
      }
    }
    catch (error) {
      console.error('获取文件信息失败:', error)
      throw error instanceof Error ? error : new Error('获取文件信息失败')
    }
  }

  /**
   * 生成预签名 URL
   */
  async generatePresignedUrl(
    key: string,
    expiresInSeconds: number = 3600,
  ): Promise<string> {
    try {
      const result = await new Promise<any>((resolve, reject) => {
        this.obsClient.createSignedUrlSync(
          {
            Method: 'GET',
            Bucket: this.config.bucket,
            Key: key,
            Expires: expiresInSeconds,
          },
          (err: any, data: any) => {
            if (err)
              reject(err)
            else resolve(data)
          },
        )
      })

      return result.SignedUrl
    }
    catch (error) {
      console.error('生成预签名 URL 失败:', error)
      throw error instanceof Error ? error : new Error('生成预签名 URL 失败')
    }
  }

  /**
   * 设置对象属性
   * @param options
   */
  async setObjectMetadata(options: SetMetadataOptions): Promise<void> {
    const key = options.key
    const disposition = options.contentDisposition

    try {
      await new Promise<void>((resolve, reject) => {
        this.obsClient.setObjectMetadata(
          {
            Bucket: this.config.bucket,
            Key: key,
            ContentDisposition: disposition,
            MetadataDirective: options?.MetadataDirective || 'REPLACE_NEW',
          },
          (err: any, result: any) => {
            if (err) {
              console.error('设置对象元数据失败:', err)
              reject(
                new Error(`设置元数据失败: ${err.message || err.code || '未知错误'}`),
              )
            }
            else {
              resolve(result)
            }
          },
        )
      })
    }
    catch (error) {
      console.error('设置对象元数据失败:', error)
      throw error instanceof Error ? error : new Error('设置对象元数据失败')
    }
  }

  /**
   * 构建访问 URL
   */
  private buildAccessUrl(key: string): string {
    if (this.config.customDomain) {
      return `${this.config.customDomain}/${key}`
    }

    const endpoint
      = this.config.endpoint
        || `https://obs.${this.config.region}.myhuaweicloud.com`
    return `${endpoint.replace('obs.', `${this.config.bucket}.obs.`)}/${key}`
  }

  /**
   * 生成随机文件名
   */
  private generateFileName(originalName: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    const ext = originalName.split('.').pop()
    const dateStr = new Date().toISOString().slice(0, 10).replace(/-/g, '')
    return `uploads/${dateStr}/${timestamp}-${random}.${ext}`
  }

  /**
   * 获取文件 MIME 类型
   */
  private getMimeType(fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase()
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      mp4: 'video/mp4',
      mp3: 'audio/mpeg',
      zip: 'application/zip',
      txt: 'text/plain',
    }
    return mimeTypes[ext || ''] || 'application/octet-stream'
  }

  /**
   * 关闭客户端连接
   */
  close(): void {
    if (this.obsClient) {
      this.obsClient.close()
    }
  }
}

export default HuaweiOBSUploader
