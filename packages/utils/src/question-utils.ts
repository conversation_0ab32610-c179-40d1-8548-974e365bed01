/**
 * 题目相关的工具函数
 */

/**
 * 将correctAnswer字符串转换为数组
 * @param correctAnswer 正确答案字符串，支持多种分隔符格式
 * @returns 正确答案数组
 *
 * @example
 * parseCorrectAnswer("A|B|D") // ["A", "B", "D"]
 * parseCorrectAnswer("A,B,D") // ["A", "B", "D"]
 * parseCorrectAnswer("A;B;D") // ["A", "B", "D"]
 * parseCorrectAnswer("A") // ["A"]
 * parseCorrectAnswer("") // []
 * parseCorrectAnswer(undefined) // []
 */
export function parseCorrectAnswer(correctAnswer: string | undefined): string[] {
  if (!correctAnswer)
    return []

  // 支持多种分隔符：管道符|、逗号,、分号;
  const separators = ['|', ',', ';']

  for (const separator of separators) {
    if (correctAnswer.includes(separator)) {
      return correctAnswer.split(separator).map(item => item.trim()).filter(Boolean)
    }
  }

  // 如果没有分隔符，返回单个元素的数组
  return [correctAnswer.trim()]
}

/**
 * 将正确答案数组转换为字符串
 * @param answers 正确答案数组
 * @param separator 分隔符，默认为管道符|
 * @returns 正确答案字符串
 *
 * @example
 * formatCorrectAnswer(["A", "B", "D"]) // "A|B|D"
 * formatCorrectAnswer(["A", "B", "D"], ",") // "A,B,D"
 * formatCorrectAnswer([]) // ""
 */
export function formatCorrectAnswer(answers: string[], separator: string = '|'): string {
  if (!Array.isArray(answers) || answers.length === 0)
    return ''

  return answers.filter(Boolean).join(separator)
}

/**
 * 检查用户答案是否正确
 * @param userAnswer 用户答案数组
 * @param correctAnswer 正确答案字符串或数组
 * @returns 是否正确
 *
 * @example
 * isAnswerCorrect(["A", "B"], "A|B|D") // false
 * isAnswerCorrect(["A", "B", "D"], "A|B|D") // true
 * isAnswerCorrect(["A", "B", "D"], ["A", "B", "D"]) // true
 */
export function isAnswerCorrect(
  userAnswer: string[],
  correctAnswer: string | string[],
): boolean {
  const correctAnswerArray = Array.isArray(correctAnswer)
    ? correctAnswer
    : parseCorrectAnswer(correctAnswer)

  if (userAnswer.length !== correctAnswerArray.length)
    return false

  // 排序后比较，避免顺序影响
  const sortedUserAnswer = [...userAnswer].sort()
  const sortedCorrectAnswer = [...correctAnswerArray].sort()

  return sortedUserAnswer.every((answer, index) => answer === sortedCorrectAnswer[index])
}

/**
 * 从正确答案中移除指定选项
 * @param correctAnswer 正确答案字符串
 * @param optionToRemove 要移除的选项
 * @param separator 输出分隔符，默认为管道符|
 * @returns 移除选项后的正确答案字符串
 *
 * @example
 * removeOptionFromCorrectAnswer("A|B|D", "B") // "A|D"
 * removeOptionFromCorrectAnswer("A,B,D", "B", ",") // "A,D"
 */
export function removeOptionFromCorrectAnswer(
  correctAnswer: string,
  optionToRemove: string,
  separator: string = '|',
): string {
  const answers = parseCorrectAnswer(correctAnswer).filter(answer => answer !== optionToRemove)
  return formatCorrectAnswer(answers, separator)
}

/**
 * 向正确答案中添加选项
 * @param correctAnswer 正确答案字符串
 * @param optionToAdd 要添加的选项
 * @param separator 输出分隔符，默认为管道符|
 * @returns 添加选项后的正确答案字符串
 *
 * @example
 * addOptionToCorrectAnswer("A|B", "D") // "A|B|D"
 * addOptionToCorrectAnswer("A,B", "D", ",") // "A,B,D"
 */
export function addOptionToCorrectAnswer(
  correctAnswer: string,
  optionToAdd: string,
  separator: string = '|',
): string {
  const answers = parseCorrectAnswer(correctAnswer)
  if (!answers.includes(optionToAdd)) {
    answers.push(optionToAdd)
  }
  return formatCorrectAnswer(answers, separator)
}
