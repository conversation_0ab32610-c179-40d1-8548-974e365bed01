@import './reset.css';
@import './nprogress.css';
@import './transition.css';

html,
body,
#app {
  height: 100%;
}

html {
  overflow-x: hidden;
  color: #000;
}
figure {
    display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 40px;
    margin-inline-end: 40px;
    unicode-bidi: isolate;
}
col {
    display: table-column;
    unicode-bidi: isolate;
}
colgroup {
    display: table-column-group;
    unicode-bidi: isolate;
}
.ck-editer-create table {
  max-width: 100%;
  background-color: transparent;
  border-collapse: collapse;
  border-spacing: 0;
  display: table;
  margin-inline-start:auto;
  margin-inline-end:auto
}

.ck-editer-create  table tbody {
  display: table-row-group;
  vertical-align: middle;
  border-color: #e50c0c;
}

.ck-editer-create  table tbody tr {
  display: table-row;
  vertical-align: inherit;
  border-color: inherit
}

.ck-editer-create  table tbody tr td,.ck-editer-create  table tbody tr th {
  border: 1px solid rgba(0,0,0,.15);
  padding: 5px 12px;
  display: table-cell;
  vertical-align: inherit
}