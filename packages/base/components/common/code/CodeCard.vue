<script setup lang="ts" name="CodeCard">
import dayjs from 'dayjs'
import PreviewCode from './PreviewCode.vue'

const props = withDefaults(defineProps<Props>(), {
  previewCode: true,
})

const emit = defineEmits(['preview', 'click'])

const codeImg = `data:image/png;base64,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`

interface Props {
  item: {
    /** 文件名称 */
    Name?: string
    /** 文件地址 */
    Url?: string
    /** 创建时间 */
    CreateTime?: string
    /** HTML代码 */
    HtmlCode?: string
  }
  /** 预览代码 */
  previewCode?: boolean
}

const previewModal = ref({
  html: '',
  show: false,
})

function onTapCard() {
  emit('click', props.item)
  if (props.previewCode) {
    //
    previewModal.value = {
      html: props.item.Url || props.item.HtmlCode || '',
      show: true,
    }
  }
}
</script>

<template>
  <div
    class="group relative h-120px w-380px flex flex-col cursor-pointer overflow-hidden border-1 rounded-8px border-solid bg-[linear-gradient(109deg_#fff_45.34%_#f7f3ff_102.43%)] p-16px text-[#44A9FB]"
    @click="onTapCard"
  >
    <icon-local-code-plus />
    <span
      class="line-clamp-1 mt-8px max-w-[220px] text-xl text-[#333333] font-bold"
      :title="item.Name"
    >
      {{ item.Name }}
    </span>
    <span class="mt-8px text-sm text-[#b8acac] font-bold">创建时间：
      {{
        item.CreateTime ? dayjs(item.CreateTime).format("mm:ss") : "-"
      }}
    </span>
    <img
      :src="codeImg"
      class="absolute right-0px top-20px h-136px w-140px transition-all duration-300 group-hover:scale-120 group-hover:-transform-rotate-15"
      alt=""
    >

    <PreviewCode v-model:modal="previewModal" />
  </div>
</template>

<style scoped></style>
