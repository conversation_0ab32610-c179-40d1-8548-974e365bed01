<script setup lang="ts" name="PreviewCode">
const modal = defineModel<{
  html: string
  show: boolean
}>('modal')

const previewDoc = ref('')
const previewUrl = ref('')

watch(modal, () => {
  previewUrl.value = ''
  previewDoc.value = ''
  if (!modal.value?.html) {
    return
  }
  const isUrl = ['http', 'https'].some(item => modal.value!.html.startsWith(item))
  if (isUrl) {
    fetch(modal.value!.html)
      .then(res => res.text())
      .then((text) => {
        previewDoc.value = text
      })
      .catch(() => {
        previewUrl.value = modal.value!.html
      })
  }
  else {
    previewDoc.value = modal.value!.html
  }
}, { deep: true, immediate: true })
</script>

<template>
  <NDrawer
    v-model:show="modal!.show"
    placement="right"
    title="预览"
    width="80vw"
  >
    <template v-if="modal?.show && modal.html">
      <iframe v-if="previewDoc" class="h-full w-full" :srcdoc="previewDoc" />
      <iframe v-else-if="previewUrl" class="h-full w-full" :src="previewUrl" />
    </template>
  </NDrawer>
</template>

<style scoped></style>
