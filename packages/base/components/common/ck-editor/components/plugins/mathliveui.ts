import '../theme/mathlive.css'
import { <PERSON>tonView, CKEditorError, ClickObserver, Plugin, global } from 'ckeditor5'
import MathliveEditing from './mathliveediting'
import { MathlivePanelCommand } from './mathlivecommand'

const mathKeystroke = 'Ctrl+M'
const pluginScopeName = '_ckeditor5Mathlive'

interface pluginScopeType {
  [pluginScopeName]: {
    panelCommand: MathlivePanelCommand
  }
}

export default class MathliveUI extends Plugin {
  public static get requires() {
    return [MathliveEditing] as const
  }

  public static get pluginName() {
    return 'MathliveUI' as const
  }

  public mathPanelRoot: (HTMLElement & pluginScopeType) | null = null
  public mathPanelRootDestroy: (() => void) | undefined = undefined

  public init(): void {
    const editor = this.editor
    editor.editing.view.addObserver(ClickObserver)

    this._createToolbarMathButton()

    this._createMathPanelRoot()

    this._enableUserPopupsInteractions()

    this._listenEditorEvents()
  }

  public _showUI(): void {
    if (!this.mathPanelRoot) {
      return
    }

    const panelCommand = this.mathPanelRoot[pluginScopeName].panelCommand

    panelCommand.execute(this.mathPanelRoot)
  }

  public _hideUI(): void {
    if (!this.mathPanelRoot) {
      return
    }

    const panelCommand = this.mathPanelRoot[pluginScopeName].panelCommand

    panelCommand.fire('close')
  }

  private _createToolbarMathButton() {
    const editor = this.editor
    const mathliveCommand = editor.commands.get('mathlive')
    if (!mathliveCommand) {
      /**
       * Mathlive command not found
       * @error plugin-load
       */
      throw new CKEditorError('plugin-load', { pluginName: 'mathlive' })
    }
    const t = editor.t

    // Handle the `Ctrl+M` keystroke and show the panel.
    editor.keystrokes.set(mathKeystroke, (_keyEvtData, cancel) => {
      // Prevent focusing the search bar in FF and opening new tab in Edge. #153, #154.
      cancel()

      this._showUI()
    })

    this.editor.ui.componentFactory.add('mathlive', (locale) => {
      const button = new ButtonView(locale)

      button.label = t('Insert math')
      button.icon = '<svg class="icon icon-tabler icon-tabler-math" fill="none" height="20" stroke="currentColor" stroke-linecap="round"\n'
        + '     stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg">\n'
        + '  <path d="M0 0h24v24H0z" fill="none" stroke="none"/>\n'
        + '  <path fill="none" d="M16 13l4 4m0 -4l-4 4"/>\n'
        + '  <path fill="none" d="M20 5h-7l-4 14l-3 -6h-2"/>\n'
        + '</svg>'
      button.keystroke = mathKeystroke
      button.tooltip = true
      button.isToggleable = true

      this.listenTo(button, 'execute', () => {
        this._showUI()
      })

      return button
    })
  }

  private _createMathPanelRoot() {
    const editor = this.editor

    const panelRoot = global.document.createElement(
      'div',
    ) as unknown as HTMLElement & pluginScopeType
    panelRoot.className = 'ck-mathlive-panelhook'

    panelRoot[pluginScopeName] = {
      panelCommand: new MathlivePanelCommand(editor),
    }

    this.mathPanelRoot = panelRoot
  }

  private _enableUserPopupsInteractions() {
    const editor = this.editor
    const mathliveConfig = editor.config.get('mathlive')!
    const viewDocument = editor.editing.view.document
    this.listenTo(viewDocument, 'click', () => {
      const mathliveCommand = editor.commands.get('mathlive')
      if (
        mathliveConfig.openPanelWhenEquationSelected
        && mathliveCommand?.isEnabled
        && mathliveCommand.value
      ) {
        this._showUI()
      }
    })
  }

  private _listenEditorEvents() {
    const editor = this.editor

    editor.on('change:isReadOnly', (_evt, _propertyName, isReadOnly) => {
      if (isReadOnly) {
        this._hideUI()
      }
    })

    editor.on('destroy', () => {
      const panelCommand = this.mathPanelRoot?.[pluginScopeName].panelCommand

      panelCommand?.destroy()

      this._hideUI()
    })
  }
}
