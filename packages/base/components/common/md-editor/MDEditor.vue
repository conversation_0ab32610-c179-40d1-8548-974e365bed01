<script setup lang="ts">
import { Crepe } from '@milkdown/crepe'
import type { Editor } from '@milkdown/kit/core'

import '@milkdown/crepe/theme/common/style.css'
import '@milkdown/crepe/theme/frame.css'

import { editorViewCtx } from '@milkdown/kit/core'
import {
  forceUpdate as _forceUpdate,
  getMarkdown as _getMarkdown,
  insert as _insert,
  insertPos as _insertPos,
  replaceAll as _replaceAll,
  replaceRange as _replaceRange,
} from '@milkdown/kit/utils'

export type ActionType = ReturnType<typeof getSelectionCoords>

const props = withDefaults(defineProps<Props>(), {
  value: '',
  disabled: false,
  showAction: true,
})

const emit = defineEmits<{
  'update:value': [value: string]
  'update:disabled': [value: boolean]
  'onAction': [action: 'prefect' | 'expand' | 'reduce', {
    text: string
    coords: ActionType
  }]
}>()

const iconExpand = '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n'
  + '<path d="M13.0203 5.4477L10.3678 2.64223C10.2384 2.50538 10.0573 2.42326 9.86322 2.42326C9.66913 2.42326 9.48798 2.50538 9.35859 2.64223L2.85028 9.53959C2.72089 9.67644 2.64325 9.86804 2.64325 10.0733L2.66913 12.8651C2.66913 13.2757 2.97967 13.6041 3.36784 13.6041L6.00739 13.6315C6.20148 13.6315 6.38262 13.5494 6.51201 13.4125L13.0203 6.50147C13.2921 6.21408 13.2921 5.73509 13.0203 5.4477ZM10.1738 7.71945C10.1479 7.74682 10.1091 7.78788 10.0702 7.81525L9.55268 8.34897L8 9.9912L5.80037 12.3451L3.87246 12.3314L3.85952 10.2923L8.2329 5.66667L10.1738 7.71945ZM11.8041 5.98143L11.0277 6.80254L9.07394 4.74976L9.86322 3.91496L11.8041 5.98143ZM3.79482 4.25709L2.95379 5.14663L1 3.08016L2.95379 1L3.79482 1.88954L3.27726 2.43695H6.87431V3.70968H3.27726L3.79482 4.25709ZM9.11275 12.2903H12.7227L12.1922 11.7429L13.0462 10.8534L15 12.9335L13.0462 15L12.1922 14.1105L12.7227 13.563H9.11275V12.2903Z" fill="#464646"/>\n'
  + '</svg>\n'

const iconPrefect = '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n'
  + '<path d="M8.70978 9.34344C8.61398 9.38338 8.52943 9.4462 8.46353 9.52639C8.39763 9.60659 8.35239 9.70171 8.33178 9.80344L7.28178 11.9954L5.52878 10.1534L5.45578 10.0854C5.35165 10.0025 5.22681 9.94956 5.09478 9.93244L2.57478 9.59544L3.78478 7.36044L3.82478 7.26844C3.87198 7.14412 3.88443 7.0093 3.86078 6.87844L3.40178 4.37644L5.90178 4.83644C6.06678 4.86644 6.23578 4.84044 6.38278 4.76044L8.61978 3.55044V3.54944L8.95678 6.07144L8.97678 6.16944C9.01191 6.29714 9.08103 6.41292 9.17678 6.50444L11.0198 8.25844L8.82778 9.30844C8.78771 9.31664 8.74853 9.32869 8.71078 9.34444V9.34344H8.70978ZM10.0508 10.1294L12.2698 9.06744L12.3578 9.01744C12.4492 8.95642 12.5258 8.87563 12.5819 8.78104C12.6379 8.68645 12.672 8.58047 12.6816 8.47094C12.6912 8.36141 12.6761 8.25112 12.6373 8.14822C12.5986 8.04532 12.5372 7.95244 12.4578 7.87644L10.1898 5.71644L9.77478 2.61244C9.75931 2.49468 9.71543 2.38245 9.6469 2.28544C9.57838 2.18843 9.48728 2.10955 9.38146 2.05561C9.27564 2.00166 9.15829 1.97428 9.03953 1.97582C8.92077 1.97736 8.80416 2.00777 8.69978 2.06444L5.94578 3.55344L2.86378 2.98944L2.76378 2.97744C2.65381 2.97261 2.54416 2.99264 2.443 3.03605C2.34184 3.07946 2.25177 3.14512 2.1795 3.22816C2.10723 3.31119 2.05462 3.40946 2.02558 3.51564C1.99654 3.62182 1.99182 3.73318 2.01178 3.84144L2.57678 6.92244L1.08678 9.67744L1.04678 9.76844C1.00806 9.87149 0.993077 9.98194 1.00295 10.0916C1.01282 10.2012 1.0473 10.3072 1.10381 10.4017C1.16032 10.4962 1.23741 10.5767 1.32934 10.6372C1.42127 10.6978 1.52567 10.7368 1.63478 10.7514L4.74078 11.1644L6.90078 13.4344L6.97378 13.5024C7.05979 13.5715 7.16033 13.6202 7.26785 13.6448C7.37537 13.6695 7.48708 13.6694 7.59457 13.6447C7.70207 13.62 7.80257 13.5712 7.88851 13.502C7.97446 13.4329 8.04361 13.3452 8.09078 13.2454L9.15278 11.0284L12.4098 14.2844C12.4687 14.3434 12.5387 14.3902 12.6158 14.4221C12.6928 14.454 12.7754 14.4704 12.8588 14.4704C12.9422 14.4704 13.0247 14.454 13.1018 14.4221C13.1788 14.3902 13.2488 14.3434 13.3078 14.2844C13.3667 14.2255 13.4135 14.1555 13.4454 14.0784C13.4773 14.0014 13.4938 13.9188 13.4938 13.8354C13.4938 13.7521 13.4773 13.6695 13.4454 13.5924C13.4135 13.5154 13.3667 13.4454 13.3078 13.3864L10.0508 10.1294ZM13.3378 1.21744C13.2694 1.13894 13.1827 1.0785 13.0853 1.04146C12.988 1.00442 12.8831 0.991927 12.7798 1.00507C12.6765 1.01822 12.578 1.05661 12.4931 1.11684C12.4081 1.17708 12.3394 1.25731 12.2928 1.35044L11.8038 2.32644C11.7316 2.47645 11.7214 2.64882 11.7752 2.80633C11.8291 2.96384 11.9427 3.09386 12.0916 3.1683C12.2405 3.24274 12.4127 3.25565 12.571 3.20422C12.7293 3.15279 12.8611 3.04117 12.9378 2.89344L13.4258 1.91744C13.482 1.80514 13.5036 1.67869 13.4879 1.55409C13.4723 1.4295 13.42 1.31234 13.3378 1.21744ZM14.9998 4.90444C15.0023 4.80005 14.979 4.69666 14.9319 4.60344C14.8849 4.51023 14.8155 4.43008 14.73 4.37012C14.6445 4.31016 14.5456 4.27225 14.4419 4.25974C14.3382 4.24724 14.2331 4.26053 14.1358 4.29844L13.1178 4.69144C13.0401 4.72158 12.9691 4.76672 12.9089 4.82429C12.8487 4.88186 12.8004 4.95073 12.7667 5.02697C12.7331 5.1032 12.7149 5.18532 12.713 5.26861C12.7111 5.35191 12.7256 5.43476 12.7558 5.51244C12.7859 5.59012 12.8311 5.6611 12.8886 5.72133C12.9462 5.78156 13.0151 5.82986 13.0913 5.86347C13.1675 5.89709 13.2497 5.91536 13.333 5.91724C13.4163 5.91912 13.4991 5.90458 13.5768 5.87444L14.5938 5.48144C14.711 5.4363 14.8122 5.35736 14.8845 5.25463C14.9567 5.1519 14.9969 5.03002 14.9998 4.90444Z" fill="#464646"/>\n'
  + '</svg>\n'

const iconReduce = '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\n'
  + '<path d="M5.61609 2C5.79222 2 5.96114 2.07202 6.08569 2.20021C6.21024 2.32841 6.2802 2.50227 6.2802 2.68357C6.2802 2.86486 6.21024 3.03873 6.08569 3.16692C5.96114 3.29511 5.79222 3.36713 5.61609 3.36713H4.80366V12.6329H5.61609C5.79222 12.6329 5.96114 12.7049 6.08569 12.8331C6.21024 12.9613 6.2802 13.1351 6.2802 13.3164C6.2802 13.4977 6.21024 13.6716 6.08569 13.7998C5.96114 13.928 5.79222 14 5.61609 14H2.66411C2.48798 14 2.31906 13.928 2.19451 13.7998C2.06997 13.6716 2 13.4977 2 13.3164C2 13.1351 2.06997 12.9613 2.19451 12.8331C2.31906 12.7049 2.48798 12.6329 2.66411 12.6329H3.47544V3.36713H2.66411C2.48798 3.36713 2.31906 3.29511 2.19451 3.16692C2.06997 3.03873 2 2.86486 2 2.68357C2 2.50227 2.06997 2.32841 2.19451 2.20021C2.31906 2.07202 2.48798 2 2.66411 2H5.61609ZM14.3359 5.37909C14.512 5.37909 14.6809 5.45111 14.8055 5.57931C14.93 5.7075 15 5.88137 15 6.06266C15 6.24395 14.93 6.41782 14.8055 6.54601C14.6809 6.67421 14.512 6.74623 14.3359 6.74623H7.41473C7.2386 6.74623 7.06968 6.67421 6.94513 6.54601C6.82059 6.41782 6.75062 6.24395 6.75062 6.06266C6.75062 5.88137 6.82059 5.7075 6.94513 5.57931C7.06968 5.45111 7.2386 5.37909 7.41473 5.37909H14.3359ZM7.41805 9.4053L11.8908 9.42808C12.067 9.42899 12.2355 9.50188 12.3595 9.63071C12.4834 9.75955 12.5525 9.93377 12.5516 10.1151C12.5508 10.2964 12.4799 10.4699 12.3548 10.5974C12.2296 10.725 12.0603 10.7961 11.8842 10.7952L7.41252 10.7724C7.23638 10.7717 7.06775 10.6989 6.94373 10.5702C6.8197 10.4415 6.75044 10.2673 6.75117 10.086C6.7519 9.90472 6.82258 9.73115 6.94764 9.60349C7.0727 9.47584 7.24192 9.40454 7.41805 9.4053Z" fill="#464646"/>\n'
  + '</svg>\n'

interface Props {
  value: string
  disabled?: boolean
  showAction?: boolean
}

const containerRef = ref()

const editorRef = shallowRef<Editor>()
const crepeRef = shallowRef<Crepe>()

// 添加状态标记，防止循环更新
const isInternalUpdate = ref(false)

function getEditorView() {
  return editorRef.value!.ctx.get(editorViewCtx)
}

function getSelectionCoords() {
  const view = getEditorView()
  const { state } = view
  const { selection } = state

  if (selection.empty) {
    // 获取光标位置
    return {
      left: 0,
      top: 0,
      right: 0,
      bottom: 0,
    }
  }
  else {
    // 获取选区的起始和结束坐标
    const startCoords = view.coordsAtPos(selection.from)
    const endCoords = view.coordsAtPos(selection.to)

    return {
      start: startCoords,
      end: endCoords,
      // 选区的边界框
      boundingBox: {
        left: Math.min(startCoords.left, endCoords.left),
        top: Math.min(startCoords.top, endCoords.top),
        right: Math.max(startCoords.right, endCoords.right),
        bottom: Math.max(startCoords.bottom, endCoords.bottom),
      },
    }
  }
}

async function initEditor() {
  // Choose your preferred theme
  if (crepeRef.value) {
    return
  }

  // Create editor instance
  const crepe = new Crepe({
    root: containerRef.value,
    defaultValue: props.value ?? '',
    features: {
      [Crepe.Feature.ImageBlock]: false,
    },
    featureConfigs: {
      [Crepe.Feature.Toolbar]: {
        buildToolbar: (builder) => {
          builder.clear()
          if (!props.showAction) {
            return
          }
          const group3 = builder.addGroup('custom', '')
          group3.addItem('perfect', {
            active: _ctx => true,
            icon: `${iconPrefect} <span style="margin-left: 4px">润色</span>`,
            onRun: (_ctx) => {
              emit('onAction', 'prefect', {
                text: getSelectedText(),
                coords: getSelectionCoords(),
              })
            },
          })
          group3.addItem('expand', {
            active: _ctx => true,
            icon: `${iconExpand} <span style="margin-left: 4px">扩写</span>`,
            onRun: (_ctx) => {
              emit('onAction', 'expand', {
                text: getSelectedText(),
                coords: getSelectionCoords(),
              })
            },
          })
          group3.addItem('reduce', {
            active: _ctx => true,
            icon: `${iconReduce} <span style="margin-left: 4px">精简</span>`,
            onRun: (_ctx) => {
              emit('onAction', 'reduce', {
                text: getSelectedText(),
                coords: getSelectionCoords(),
              })
            },
          })
        },
      },
      [Crepe.Feature.Placeholder]: {
        text: '请输入...',
        mode: 'block',
      },
      [Crepe.Feature.Latex]: {
        katexOptions: {
          throwOnError: false,
          displayMode: true,
        },
      },
    },
  })
  crepeRef.value = crepe
  // 设置是否只读
  crepe.setReadonly(props.disabled)

  crepe.on((listener) => {
    listener.markdownUpdated(() => {
      if (props.disabled) {
        return
      }
      isInternalUpdate.value = true // 内部更新触发
      emit('update:value', `${getMarkdown()}`)
    })

    listener.updated(() => {

    })

    listener.focus(() => {
    })

    listener.blur(() => {
    })
  })

  // Initialize the editor
  editorRef.value = await crepe.create()
}

onMounted(() => {
  initEditor()
})

onUnmounted(() => {
  crepeRef.value?.destroy()
})

// 更新外部传入的数据
watch(() => props.value, (newValue) => {
  if (isInternalUpdate.value) {
    isInternalUpdate.value = false
    return
  }

  replaceAll(newValue)
})

watch(() => props.disabled, (newValue) => {
  setReadOnly(newValue)
})

function getMarkdown() {
  if (!editorRef.value) {
    return ''
  }
  return _getMarkdown()(editorRef.value.ctx)
}

function insert(content: string, inline: boolean = false) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_insert(content, inline))
}

function insertPos(content: string, pos: number, inline: boolean = false) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_insertPos(content, pos, inline))
}

function replaceAll(content: string) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_replaceAll(content))
}

function replaceRange(content: string, range: { from: number, to: number }) {
  if (!editorRef.value) {
    return
  }
  editorRef.value.action(_replaceRange(content, range))
}

function getSelection() {
  return getEditorView().state.selection
}

function getSelectedText() {
  if (!editorRef.value) {
    return ''
  }
  const view = getEditorView()
  const state = view.state
  const { from, to } = state.selection

  return state.doc.textBetween(from, to, '\n')
}

function isCursorAtEnd() {
  if (!editorRef.value) {
    return false
  }
  const docSize = getEditorView().state.doc.content.size
  const { from, empty } = getSelection()

  if (!empty)
    return false // 不是单一光标，说明是选区

  return from === docSize - 1
}

function setReadOnly(value: boolean) {
  crepeRef.value?.setReadonly(value)
}

function forceUpdate() {
  editorRef.value?.action(_forceUpdate())
}

function recreate() {
  crepeRef.value?.destroy()
  editorRef.value = undefined
  crepeRef.value = undefined
  initEditor()
}

defineExpose({
  insert,
  insertPos,
  replaceAll,
  replaceRange,
  getMarkdown,
  getSelectionCoords,
  getSelection,
  setReadOnly,
  isCursorAtEnd,
  forceUpdate,
  recreate,
  getSelectedText,
})
</script>

<template>
  <div ref="containerRef" class="size-full" />
</template>

<style lang="scss">
.milkdown {
  min-height: 100%;

  .ProseMirror {
    padding: 32px 88px;
  }

  .milkdown-toolbar {
    background-color: white;
    .toolbar-item {
      width: 64px;
      color: #464646;
    }
  }
}
</style>
