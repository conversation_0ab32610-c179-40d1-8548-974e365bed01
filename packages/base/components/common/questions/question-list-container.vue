<script setup lang="ts">
import type { TransformToVoQuestionData } from '@sa/utils'
import QsProvide from './qs-provide/index.vue'
import QuestionItemContainer from './question-item-container.vue'

defineOptions({
  name: 'QuestionListContainer',
})
const props = defineProps<Props>()
interface Props {
  type?: Question.QuestionType
}
const questionList = defineModel<TransformToVoQuestionData[]>('questionList', {
  default: () => [],
})
</script>

<template>
  <QsProvide :type="props.type">
    <template v-for="(_item, index) in questionList" :key="_item.id">
      <QuestionItemContainer
        v-model:item-info="questionList[index]"
        :type="props.type"
      />
    </template>
  </QsProvide>
</template>
