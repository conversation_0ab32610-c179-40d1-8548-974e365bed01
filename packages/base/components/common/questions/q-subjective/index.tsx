import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import { NFormItem } from 'naive-ui'
import styles from './index.module.scss'

export default defineComponent({
  name: 'Subjective',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
  },
  emits: ['update:modelValue', 'update:item'],
  setup(props, { emit }) {
    const isdisabled = computed(() => props.type === 'preview')

    // 创建可编辑的参考答案计算属性
    const analysis = computed({
      get: () => props.item.correctAnswer || '',
      set: (value: string) => {
        // 更新题目数据中的参考答案
        const updatedItem = { ...props.item, correctAnswer: value }
        emit('update:item', updatedItem)
      },
    })

    return () => {
      return (
        <div class={styles.subjectiveContainer}>
          {!isdisabled.value && (
            <NFormItem label="参考答案">
              <CKEditor v-model:editorValue={analysis.value}></CKEditor>
            </NFormItem>
          )}
        </div>
      )
    }
  },
})
