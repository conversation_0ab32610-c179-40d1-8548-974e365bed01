import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { NButton, NPopconfirm } from 'naive-ui'
import SvgIcon from '@sa/components/custom/svg-icon.vue'
import { useQuestionsForm } from '@sa/hooks'
import type { TransformToVoQuestionData } from '@sa/utils'
import { parseCorrectAnswer, removeOptionFromCorrectAnswer } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import styles from './index.module.scss'

export default defineComponent({
  name: 'MultipleChoice',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
  },
  emits: ['update:item'],
  setup(props, { emit, expose }) {
    // 修改 item 属性的方法
    const updateItemProperty = (property: keyof TransformToVoQuestionData, value: any) => {
      const updatedItem = { ...props.item, [property]: value }
      emit('update:item', updatedItem)
    }
    const isdisabled = computed(() => props.type === 'preview')
    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    // 系统答案
    const systemAnswer = computed(() => parseCorrectAnswer(props.item.correctAnswer))
    // 编辑模式下的选项改变 - 根据系统答案进行设置
    const handleChange = (value: string) => {
      if (mergedDisabled.value)
        return
      const currentSystemValues = [...systemAnswer.value]
      const selected = currentSystemValues.includes(value)
      if (selected) {
        // 检查取消选择后是否还有至少2个正确答案
        const remainingAnswers = currentSystemValues.filter(item => item !== value)
        if (remainingAnswers.length < 2) {
          window.$message?.warning('多选题必须至少保留2个正确答案')
          return
        }
        // 如果系统答案中包含该选项，则从系统答案中移除
        const updatedSystemAnswer = remainingAnswers.join(',')
        updateItemProperty('correctAnswer', updatedSystemAnswer)
      }
      else {
        // 如果系统答案中不包含该选项，则添加到系统答案中
        const updatedSystemAnswer = [...currentSystemValues, value].join(',')
        updateItemProperty('correctAnswer', updatedSystemAnswer)
      }
    }

    // 删除选项
    const handleDeleteOption = (optionValue: string) => {
      if (!props.item.options || props.item.options.length <= 2) {
        // 至少保留2个选项
        return
      }

      // 检查是否为正确答案，如果是则不允许删除
      if (systemAnswer.value.includes(optionValue)) {
        window.$message?.warning('不能删除正确答案选项，请先修改正确答案')
        return
      }

      // 过滤掉要删除的选项
      const filteredOptions = props.item.options.filter((option: any) => option.value !== optionValue)

      // 直接使用过滤后的选项，不重新排序
      updateItemProperty('options', filteredOptions)

      // 处理正确答案的更新 - 简单移除被删除的选项
      if (props.item.correctAnswer && props.item.correctAnswer.includes(optionValue)) {
        const updatedCorrectAnswer = removeOptionFromCorrectAnswer(props.item.correctAnswer, optionValue)
        updateItemProperty('correctAnswer', updatedCorrectAnswer)
      }
    }
    const renderOptionLabel = (option: any) => {
      if (!isdisabled.value) {
        return (
          <CKEditor
            v-model:editorValue={option.label}
            minHeight={32}
          />
        )
      }
      else {
        return (
          <span
            class={[styles.choiceItemLabel, 'contents', 'ck-editer-create ']}
            v-html={option.label}
            v-katex
          />
        )
      }
    }
    // 渲染函数
    const renderContent = () => (
      <ul class={styles.root}>
        {props.item.options?.map((option: any, index: number) => (
          <li key={index}>
            <div class={styles.choiceItemWrapper}>
              <label class={styles.choiceItem}>
                <div
                  class={[
                    styles.choiceItemQn,
                    !isdisabled.value && styles.cursor,
                    !isdisabled.value && (systemAnswer.value.includes(option.value) ? styles.choiceItemQnChecked : ''),
                  ]}
                  onClick={() => {
                    if (mergedDisabled.value) {
                      return
                    }
                    handleChange(option.value)
                  }}
                >
                  <span>{option.value}</span>
                </div>
                {renderOptionLabel(option)}
              </label>
              {/* 编辑模式下显示删除按钮 */}
              {props.type === 'edit' && props.item.options && props.item.options.length > 2 && (
                systemAnswer.value.includes(option.value)
                  ? (
                      // 正确答案选项显示禁用的删除按钮
                      <NButton
                        size="small"
                        quaternary={true}
                        disabled={true}
                        class={styles.deleteButton}
                      >
                        <SvgIcon icon="mdi:close" />
                      </NButton>
                    )
                  : (
                      // 非正确答案选项显示可点击的删除按钮
                      <NPopconfirm
                        onPositiveClick={() => handleDeleteOption(option.value)}
                        positiveText="确认删除"
                        negativeText="取消"
                      >
                        {{
                          trigger: () => (
                            <NButton
                              size="small"
                              quaternary={true}
                              class={styles.deleteButton}
                            >
                              <SvgIcon icon="mdi:close" />
                            </NButton>
                          ),
                          default: () => `确定要删除选项 ${option.value} 吗？`,
                        }}
                      </NPopconfirm>
                    )
              )}
            </div>
          </li>
        ))}
      </ul>
    )

    // 暴露修改 item 属性的方法
    expose({
      updateItemProperty,
    })

    return {
      renderContent,
    }
  },
  render() {
    return this.renderContent()
  },
})
